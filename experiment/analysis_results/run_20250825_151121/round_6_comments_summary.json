{"round": 6, "new_comments": [{"comment_id": "comment_df20afeb", "user_id": "user_71", "post_id": "post_2", "content": "看到这个帖子有点心动，但冷静下来想想，这种“终结者”“内部爆料”的说法太夸张了。糖尿病是复杂慢性病，哪有那么容易被一粒药搞定。甘平宁确实有新进展，但得看正规临床数据，别被情绪带跑偏。理性看待，科学用药才是正道。", "created_at": 1756109894, "likes_count": 0}, {"comment_id": "comment_798a8fee", "user_id": "user_90", "post_id": "post_7", "content": "这是一个模拟的API响应，用于测试。发生异常: HTTPConnectionPool(host='**************', port=8060): Max retries exceeded with url: /v1/chat/completions (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x7fc0b9c59dd0>, 'Connection to ************** timed out. (connect timeout=30)'))", "created_at": 1756112550, "likes_count": 0}], "new_replies": [{"comment_id": "comment_f0aa4823", "user_id": "user_58", "post_id": "post_3", "content": "看到这评论有点心累，专家也不是全靠钱办事，但确实有些报告水分大。关键还是得看数据透明、流程公开，不能一竿子打翻一船人，也别轻易给谁贴标签。", "parent_comment_id": "comment_post_3_2", "created_at": 1756109861, "likes_count": 0}], "comments_stats": {"total_before": 41, "total_after": 44, "new_comments_count": 2, "new_replies_count": 1, "comments_by_user": {"user_71": 1, "user_90": 1}, "replies_by_user": {"user_58": 1}, "comments_by_post": {"post_2": 1, "post_7": 1}, "replies_by_post": {"post_3": 1}}}