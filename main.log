2025-08-25 20:47:01,812 - Main - INFO - 启动认知智能体模拟系统...
2025-08-25 20:47:01,812 - Engine - INFO - Initializing Engine and its modules...
2025-08-25 20:47:01,812 - src.modules.agent_module - INFO - AgentModule initialized.
2025-08-25 20:47:01,812 - src.modules.content_module - INFO - ContentModule initialized.
2025-08-25 20:47:01,812 - src.modules.agent_module - INFO - AgentModule initialized.
2025-08-25 20:47:01,812 - NotificationModule - INFO - NotificationModule initialized.
2025-08-25 20:47:01,813 - src.modules.belief_module - INFO - BeliefModule initialized with dependencies.
2025-08-25 20:47:01,813 - src.modules.belief_module - INFO - 多线程配置: {'enabled': True, 'max_workers': 4, 'timeout': 30}
2025-08-25 20:47:01,813 - UserManagement - INFO - UserManagementModule initialized.
2025-08-25 20:47:01,813 - Engine - INFO - Engine and modules initialized successfully.
2025-08-25 20:47:01,813 - Simulator - INFO - Simulator initialized with initial_bias_strength=1.
2025-08-25 20:47:01,813 - NotificationModule - INFO - Simulator set for NotificationModule.
2025-08-25 20:47:01,813 - Engine - INFO - Simulator setup completed and linked with notification module.
2025-08-25 20:47:01,814 - BeliefLogger - INFO - 实验结果将保存到: /home/<USER>/workspace/virtualcommunity/experiment/analysis_results/run_20250825_204701
2025-08-25 20:47:01,815 - BeliefLogger - INFO - 实验配置已保存到: experiment_config.json
2025-08-25 20:47:01,815 - Main - INFO - 开始运行 40 轮模拟，每轮 5 步...
2025-08-25 20:47:01,815 - Main - INFO - 开始第 1/40 轮模拟
2025-08-25 20:47:06,946 - BeliefLogger - INFO - 已记录轮次 1 before 阶段的所有用户信念到 round_1_before_beliefs.json
2025-08-25 20:47:07,266 - BeliefLogger - INFO - 已记录轮次 1 before 阶段的所有用户记忆到 round_1_before_memories.json
2025-08-25 20:47:07,563 - BeliefLogger - INFO - 已记录轮次 1 before 阶段的所有用户信息到 round_1_before_users.json，共 100 个用户
2025-08-25 20:47:12,837 - ExperimentLoggers - INFO - 已记录轮次 1 before 阶段的所有帖子、评论和回复到 round_1_before_posts.json
2025-08-25 20:47:12,850 - ExperimentLoggers - INFO - 已记录轮次 1 before 阶段的所有评论和回复到 round_1_before_comments.json，共 33 条
2025-08-25 20:47:12,851 - ExperimentLogger - INFO - 开始记录轮次 1 before 阶段的实验指标...
2025-08-25 20:48:34,865 - ExperimentLogger - INFO - 已记录轮次 1 before 阶段的SIR指标到 round_1_before_sir_metrics.json
2025-08-25 20:48:34,866 - ExperimentLogger - INFO -   - 计算方法: 基于用户发言内容的谣言检测（符合SIR状态转换规则）
2025-08-25 20:48:34,866 - ExperimentLogger - INFO -   - 总体易感者: 92 (0.920) - 从未发布谣言内容
2025-08-25 20:48:34,866 - ExperimentLogger - INFO -   - 总体感染者: 8 (0.080) - 发布过谣言但未发布非谣言
2025-08-25 20:48:34,866 - ExperimentLogger - INFO -   - 总体康复者: 0 (0.000) - 曾经是感染者且后来发布了非谣言
2025-08-25 20:48:34,866 - ExperimentLogger - INFO -   - 知识分子群体SIR指标: 总数=30, 易感者=28(0.933), 感染者=2(0.067), 康复者=0(0.000)
2025-08-25 20:48:34,866 - ExperimentLogger - INFO -   - 普通群众群体SIR指标: 总数=70, 易感者=64(0.914), 感染者=6(0.086), 康复者=0(0.000)
2025-08-25 20:48:37,432 - ExperimentLogger - INFO - 已记录轮次 1 before 阶段的极化指数到 round_1_before_polarization_metrics.json
2025-08-25 20:48:37,432 - ExperimentLogger - INFO -   - 总体OEI指数: 0.570
2025-08-25 20:48:37,432 - ExperimentLogger - INFO -   - 极端观点用户: 57 (0.570)
2025-08-25 20:48:37,432 - ExperimentLogger - INFO -   - 温和观点用户: 24 (0.240)
2025-08-25 20:48:37,432 - ExperimentLogger - INFO -   - 无明确观点用户: 19 (0.190)
2025-08-25 20:48:37,432 - ExperimentLogger - INFO -   - 知识分子群体OEI指数: 1.000 (总数: 30, 极端: 30, 温和: 0, 无观点: 0)
2025-08-25 20:48:37,432 - ExperimentLogger - INFO -   - 普通群众群体OEI指数: 0.386 (总数: 70, 极端: 27, 温和: 24, 无观点: 19)
2025-08-25 20:48:40,311 - ExperimentLogger - INFO - 已记录轮次 1 before 阶段的智能体演化数据到 round_1_before_agent_evolution.json
2025-08-25 20:48:40,312 - ExperimentLogger - INFO -   - 追踪用户数: 100
2025-08-25 20:48:40,330 - ExperimentLogger - INFO - 已记录轮次 1 before 阶段的网络传播分析到 round_1_before_network_propagation.json
2025-08-25 20:48:40,330 - ExperimentLogger - INFO -   - 活跃用户数: 8
2025-08-25 20:48:40,330 - ExperimentLogger - INFO -   - 内容创作者数: 8
2025-08-25 20:48:40,330 - ExperimentLogger - INFO -   - 评论者数: 0
2025-08-25 20:48:40,330 - ExperimentLogger - INFO -   - 总帖子数: 10
2025-08-25 20:48:40,330 - ExperimentLogger - INFO -   - 总评论数: 33
2025-08-25 20:48:40,330 - ExperimentLogger - INFO - 轮次 1 before 阶段的实验指标记录完成
2025-08-25 20:48:40,330 - Simulator - INFO - 设置轮数信息: 当前轮次 1/40
2025-08-25 20:48:40,330 - Simulator - INFO - Starting simulation with 5 steps...
2025-08-25 20:48:40,331 - Simulator - INFO - Simulation step 1/5
2025-08-25 20:48:41,607 - Simulator - INFO - Added CREATE_COMMENT event for user user_39 to queue. Queue size: 1
2025-08-25 20:48:41,608 - Simulator - INFO - Processing event from queue: CREATE_COMMENT for user user_39
2025-08-25 20:48:41,608 - Engine - INFO - ----- Processing action 'CREATE_COMMENT' for user 'user_39' -----
2025-08-25 20:48:49,674 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_39_5686ac40).
2025-08-25 20:48:49,674 - Engine - INFO - Step 2/4: Updating content state for action 'CREATE_COMMENT'...
2025-08-25 20:48:57,157 - src.modules.content_module - INFO - Incremented comment count for post 'post_9'.
2025-08-25 20:49:01,536 - src.modules.agent_module - INFO - 用户 'user_39' 正在 关注 帖子 'post_9'...
2025-08-25 20:49:05,882 - src.modules.agent_module - INFO - 成功处理用户 'user_39' 关注 帖子 'post_9' 的操作。
2025-08-25 20:49:05,882 - src.modules.content_module - INFO - User 'user_39' successfully followed post 'post_9'.
2025-08-25 20:49:05,882 - src.modules.content_module - INFO - Added comment 'comment_088e0c90' to post 'post_9' by user 'user_39'
2025-08-25 20:49:05,882 - src.modules.agent_module - INFO - 根据ID推断，正在为用户 'user_39' 的 'comments' 列表添加 ID 'comment_088e0c90'...
2025-08-25 20:49:09,180 - src.modules.agent_module - INFO - 成功为用户 'user_39' 添加活动。
2025-08-25 20:49:09,181 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:49:09,184 - NotificationModule - INFO - Would send notification to user 'user_3': Your post 'post_9' was commented on by user 'user_39'.
2025-08-25 20:49:09,192 - Simulator - INFO - Added READ_POST event for user user_3 to queue. Queue size: 1
2025-08-25 20:49:09,192 - Simulator - INFO - Generated READ_POST event for user user_3 based on notification.
2025-08-25 20:49:09,192 - Engine - INFO - Comment notification sent to post author.
2025-08-25 20:49:09,197 - Engine - INFO - Sent new comment notification to 1 post followers.
2025-08-25 20:49:09,197 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_39'...
2025-08-25 20:49:09,197 - src.modules.belief_module - INFO - 开始处理用户 'user_39' 的认知流程...
2025-08-25 20:49:09,213 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:49:09,213 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:49:09,213 - src.modules.belief_module - INFO - 用户 'user_39' 的短期记忆中未找到显著议题簇
2025-08-25 20:49:09,214 - Engine - INFO - Cognitive processing for user 'user_39' finished.
2025-08-25 20:49:09,214 - Engine - INFO - ----- Action 'CREATE_COMMENT' for user 'user_39' processed successfully. -----
2025-08-25 20:49:09,214 - Simulator - INFO - Processing event from queue: READ_POST for user user_3
2025-08-25 20:49:09,214 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_3' -----
2025-08-25 20:49:15,944 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_3_a3aca342).
2025-08-25 20:49:15,944 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-25 20:49:18,731 - src.modules.content_module - INFO - Incremented view count for post 'post_9'.
2025-08-25 20:49:18,731 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:49:18,732 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:49:18,732 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_3'...
2025-08-25 20:49:18,732 - src.modules.belief_module - INFO - 开始处理用户 'user_3' 的认知流程...
2025-08-25 20:49:18,747 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:49:18,747 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:49:18,747 - src.modules.belief_module - INFO - 用户 'user_3' 的短期记忆中未找到显著议题簇
2025-08-25 20:49:18,747 - Engine - INFO - Cognitive processing for user 'user_3' finished.
2025-08-25 20:49:18,747 - Engine - INFO - ----- Action 'READ_POST' for user 'user_3' processed successfully. -----
2025-08-25 20:49:18,748 - Simulator - INFO - Simulation step completed for user user_39.
2025-08-25 20:49:18,748 - Simulator - INFO - Simulation step 2/5
2025-08-25 20:49:19,033 - Simulator - INFO - Added READ_POST event for user user_35 to queue. Queue size: 1
2025-08-25 20:49:19,033 - Simulator - INFO - Processing event from queue: READ_POST for user user_35
2025-08-25 20:49:19,033 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_35' -----
2025-08-25 20:49:25,296 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_35_0047f9ac).
2025-08-25 20:49:25,296 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-25 20:49:27,983 - src.modules.content_module - INFO - Incremented view count for post 'post_4'.
2025-08-25 20:49:27,984 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:49:27,984 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:49:27,984 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_35'...
2025-08-25 20:49:27,984 - src.modules.belief_module - INFO - 开始处理用户 'user_35' 的认知流程...
2025-08-25 20:49:27,999 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:49:27,999 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:49:27,999 - src.modules.belief_module - INFO - 用户 'user_35' 的短期记忆中未找到显著议题簇
2025-08-25 20:49:27,999 - Engine - INFO - Cognitive processing for user 'user_35' finished.
2025-08-25 20:49:28,000 - Engine - INFO - ----- Action 'READ_POST' for user 'user_35' processed successfully. -----
2025-08-25 20:49:28,000 - Simulator - INFO - Simulation step completed for user user_35.
2025-08-25 20:49:28,000 - Simulator - INFO - Simulation step 3/5
2025-08-25 20:49:28,282 - Simulator - WARNING - 用户 user_48 没有信念，使用默认信念ID
2025-08-25 20:49:29,356 - Simulator - INFO - Added CREATE_COMMENT event for user user_48 to queue. Queue size: 1
2025-08-25 20:49:29,357 - Simulator - INFO - Processing event from queue: CREATE_COMMENT for user user_48
2025-08-25 20:49:29,357 - Engine - INFO - ----- Processing action 'CREATE_COMMENT' for user 'user_48' -----
2025-08-25 20:49:35,421 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_48_8b93b613).
2025-08-25 20:49:35,421 - Engine - INFO - Step 2/4: Updating content state for action 'CREATE_COMMENT'...
2025-08-25 20:49:40,930 - src.modules.content_module - INFO - Incremented comment count for post 'post_3'.
2025-08-25 20:49:46,065 - src.modules.agent_module - INFO - 用户 'user_48' 正在 关注 帖子 'post_3'...
2025-08-25 20:49:48,803 - src.modules.agent_module - INFO - 成功处理用户 'user_48' 关注 帖子 'post_3' 的操作。
2025-08-25 20:49:48,804 - src.modules.content_module - INFO - User 'user_48' successfully followed post 'post_3'.
2025-08-25 20:49:48,804 - src.modules.content_module - INFO - Added comment 'comment_14f4a285' to post 'post_3' by user 'user_48'
2025-08-25 20:49:48,804 - src.modules.agent_module - INFO - 根据ID推断，正在为用户 'user_48' 的 'comments' 列表添加 ID 'comment_14f4a285'...
2025-08-25 20:49:51,349 - src.modules.agent_module - INFO - 成功为用户 'user_48' 添加活动。
2025-08-25 20:49:51,349 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:49:51,352 - NotificationModule - INFO - Would send notification to user 'user_52': Your post 'post_3' was commented on by user 'user_48'.
2025-08-25 20:49:51,360 - Simulator - INFO - Added READ_POST event for user user_52 to queue. Queue size: 1
2025-08-25 20:49:51,360 - Simulator - INFO - Generated READ_POST event for user user_52 based on notification.
2025-08-25 20:49:51,360 - Engine - INFO - Comment notification sent to post author.
2025-08-25 20:49:51,366 - Engine - INFO - Sent new comment notification to 1 post followers.
2025-08-25 20:49:51,366 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_48'...
2025-08-25 20:49:51,366 - src.modules.belief_module - INFO - 开始处理用户 'user_48' 的认知流程...
2025-08-25 20:49:51,381 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:49:51,381 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:49:51,381 - src.modules.belief_module - INFO - 用户 'user_48' 的短期记忆中未找到显著议题簇
2025-08-25 20:49:51,381 - Engine - INFO - Cognitive processing for user 'user_48' finished.
2025-08-25 20:49:51,382 - Engine - INFO - ----- Action 'CREATE_COMMENT' for user 'user_48' processed successfully. -----
MemoryModule initialized.

【模拟步骤】选择用户: user_39 执行操作 (步骤 1/5, 轮次 1/40)
【用户行为】用户 user_39 选择了 CREATE_COMMENT 操作，评论帖子 post_9："这年头连说句实话都得掂量着？药厂黑幕一堆，高管偷偷摸摸见国外巨头，结果还被压下去？真当老百姓是傻子？..."
【事件队列】添加事件: CREATE_COMMENT (用户 user_39), 队列大小: 1
【处理事件】从队列中处理事件: CREATE_COMMENT (用户 user_39)，评论帖子 post_9："这年头连说句实话都得掂量着？药厂黑幕一堆，高管偷偷摸摸见国外巨头，结果还被压下去？真当老百姓是傻子？..."
【引擎】开始处理用户 user_39 的 CREATE_COMMENT 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_39'...
MemoryModule: Successfully created memory 'mem_user_39_5686ac40'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_39_5686ac40)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_39 的 CREATE_COMMENT 操作是否需要发送通知...
【通知】发送评论通知给帖子作者 user_3，评论内容："这年头连说句实话都得掂量着？药厂黑幕一堆，高管偷偷摸摸见国外巨头，结果还被压下去？真当老百姓是傻子？..."
【通知模块】用户 user_39 评论了帖子，发送通知给帖子作者 user_3
【通知事件】生成通知事件: 用户 user_3 接收 READ_POST 通知
【通知事件】为用户 user_3 生成浏览事件，浏览帖子 post_9："最新线索！网传瑞康生物叫停“甘平宁”项目的时间点，与其公司高管被拍到与某国外医药巨头CEO在海外秘密..."
【事件队列】添加事件: READ_POST (用户 user_3), 队列大小: 1
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_39 的认知处理...

【认知处理】开始处理用户 user_39 的认知流程
【阶段1+2】获取用户 user_39 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_39'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_39 的认知处理完成
【引擎】用户 user_39 的 CREATE_COMMENT 操作处理成功
【处理成功】事件处理成功: CREATE_COMMENT (用户 user_39)
【处理事件】从队列中处理事件: READ_POST (用户 user_3)，浏览帖子 post_9："评论内容: 这年头连说句实话都得掂量着？药厂黑幕一堆，高管偷偷摸摸见国外巨头，结果还被压下去？真当老..."
【引擎】开始处理用户 user_3 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_3'...
MemoryModule: Successfully created memory 'mem_user_3_a3aca342'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_3_a3aca342)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_3 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_3 的认知处理...

【认知处理】开始处理用户 user_3 的认知流程
【阶段1+2】获取用户 user_3 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_3'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_3 的认知处理完成
【引擎】用户 user_3 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_3)

【模拟步骤】选择用户: user_35 执行操作 (步骤 2/5, 轮次 1/40)
【用户行为】用户 user_35 选择了 READ_POST 操作，浏览帖子 post_4："事出反常必有妖！网上关于瑞康生物为利益封杀神药“甘平宁”的爆料已经铺天盖地，但至今没有任何主流媒体敢..."
【事件队列】添加事件: READ_POST (用户 user_35), 队列大小: 1
【处理事件】从队列中处理事件: READ_POST (用户 user_35)，浏览帖子 post_4："事出反常必有妖！网上关于瑞康生物为利益封杀神药“甘平宁”的爆料已经铺天盖地，但至今没有任何主流媒体敢..."
【引擎】开始处理用户 user_35 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_35'...
MemoryModule: Successfully created memory 'mem_user_35_0047f9ac'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_35_0047f9ac)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_35 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_35 的认知处理...

【认知处理】开始处理用户 user_35 的认知流程
【阶段1+2】获取用户 user_35 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_35'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_35 的认知处理完成
【引擎】用户 user_35 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_35)

【模拟步骤】选择用户: user_48 执行操作 (步骤 3/5, 轮次 1/40)
Warning: Belief 'default_belief_id' not found.
【用户行为】用户 user_48 选择了 CREATE_COMMENT 操作，评论帖子 post_3："看到这个帖子，心里挺不是滋味的。如果真有这么一款能逆转糖尿病的平价药，那对多少人意味着希望啊。但现实..."
【事件队列】添加事件: CREATE_COMMENT (用户 user_48), 队列大小: 1
【处理事件】从队列中处理事件: CREATE_COMMENT (用户 user_48)，评论帖子 post_3："看到这个帖子，心里挺不是滋味的。如果真有这么一款能逆转糖尿病的平价药，那对多少人意味着希望啊。但现实..."
【引擎】开始处理用户 user_48 的 CREATE_COMMENT 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_48'...
MemoryModule: Successfully created memory 'mem_user_48_8b93b613'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_48_8b93b613)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_48 的 CREATE_COMMENT 操作是否需要发送通知...
【通知】发送评论通知给帖子作者 user_52，评论内容："看到这个帖子，心里挺不是滋味的。如果真有这么一款能逆转糖尿病的平价药，那对多少人意味着希望啊。但现实..."
【通知模块】用户 user_48 评论了帖子，发送通知给帖子作者 user_52
【通知事件】生成通知事件: 用户 user_52 接收 READ_POST 通知
【通知事件】为用户 user_52 生成浏览事件，浏览帖子 post_3："心都碎了！一个勇敢的吹哨人揭露，瑞康生物公司本已研发出能“逆转”糖尿病的平价药“甘平宁”，却因动了资..."
【事件队列】添加事件: READ_POST (用户 user_52), 队列大小: 1
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_48 的认知处理...

【认知处理】开始处理用户 user_48 的认知流程
【阶段1+2】获取用户 user_48 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_48'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_48 的认知处理完成
【引擎】用户 user_48 的 CREATE_COMMENT 操作处理成功
【处理成功】事件处理成功: CREATE_COMMENT (用户 user_48)
2025-08-25 20:49:51,382 - Simulator - INFO - Processing event from queue: READ_POST for user user_52
2025-08-25 20:49:51,382 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_52' -----
2025-08-25 20:49:58,044 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_52_1734a87b).
2025-08-25 20:49:58,044 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-25 20:50:01,039 - src.modules.content_module - INFO - Incremented view count for post 'post_3'.
2025-08-25 20:50:01,040 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:50:01,040 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:50:01,040 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_52'...
2025-08-25 20:50:01,040 - src.modules.belief_module - INFO - 开始处理用户 'user_52' 的认知流程...
2025-08-25 20:50:01,055 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:50:01,055 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:50:01,055 - src.modules.belief_module - INFO - 用户 'user_52' 的短期记忆中未找到显著议题簇
2025-08-25 20:50:01,056 - Engine - INFO - Cognitive processing for user 'user_52' finished.
2025-08-25 20:50:01,056 - Engine - INFO - ----- Action 'READ_POST' for user 'user_52' processed successfully. -----
2025-08-25 20:50:01,056 - Simulator - INFO - Simulation step completed for user user_48.
2025-08-25 20:50:01,056 - Simulator - INFO - Simulation step 4/5
2025-08-25 20:50:03,836 - Simulator - INFO - Added CREATE_POST event for user user_54 to queue. Queue size: 1
2025-08-25 20:50:03,837 - Simulator - INFO - Processing event from queue: CREATE_POST for user user_54
2025-08-25 20:50:03,837 - Engine - INFO - ----- Processing action 'CREATE_POST' for user 'user_54' -----
2025-08-25 20:50:10,271 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_54_f66a6a2d).
2025-08-25 20:50:10,271 - Engine - INFO - Step 2/4: Updating content state for action 'CREATE_POST'...
2025-08-25 20:50:13,997 - src.modules.content_module - INFO - 帖子 'post_b76c8f9f' 由用户 'user_54' 创建成功。
2025-08-25 20:50:13,997 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:50:14,000 - NotificationModule - INFO - Would send notification to user 'user_13': User 'user_54' you follow has published a new post 'post_b76c8f9f'.
2025-08-25 20:50:14,009 - Simulator - INFO - Added READ_POST event for user user_13 to queue. Queue size: 1
2025-08-25 20:50:14,009 - Simulator - INFO - Generated READ_POST event for user user_13 based on notification.
2025-08-25 20:50:14,009 - NotificationModule - INFO - Would send notification to user 'user_84': User 'user_54' you follow has published a new post 'post_b76c8f9f'.
2025-08-25 20:50:14,017 - Simulator - INFO - Added READ_POST event for user user_84 to queue. Queue size: 2
2025-08-25 20:50:14,017 - Simulator - INFO - Generated READ_POST event for user user_84 based on notification.
2025-08-25 20:50:14,017 - NotificationModule - INFO - Would send notification to user 'user_34': User 'user_54' you follow has published a new post 'post_b76c8f9f'.
2025-08-25 20:50:14,025 - Simulator - INFO - Added READ_POST event for user user_34 to queue. Queue size: 3
2025-08-25 20:50:14,025 - Simulator - INFO - Generated READ_POST event for user user_34 based on notification.
2025-08-25 20:50:14,025 - NotificationModule - INFO - Would send notification to user 'user_42': User 'user_54' you follow has published a new post 'post_b76c8f9f'.
2025-08-25 20:50:14,033 - Simulator - INFO - Added READ_POST event for user user_42 to queue. Queue size: 4
2025-08-25 20:50:14,034 - Simulator - INFO - Generated READ_POST event for user user_42 based on notification.
2025-08-25 20:50:14,034 - NotificationModule - INFO - Would send notification to user 'user_67': User 'user_54' you follow has published a new post 'post_b76c8f9f'.
2025-08-25 20:50:14,042 - Simulator - INFO - Added READ_POST event for user user_67 to queue. Queue size: 5
2025-08-25 20:50:14,042 - Simulator - INFO - Generated READ_POST event for user user_67 based on notification.
2025-08-25 20:50:14,042 - NotificationModule - INFO - Would send notification to user 'user_93': User 'user_54' you follow has published a new post 'post_b76c8f9f'.
2025-08-25 20:50:14,050 - Simulator - INFO - Added READ_POST event for user user_93 to queue. Queue size: 6
2025-08-25 20:50:14,050 - Simulator - INFO - Generated READ_POST event for user user_93 based on notification.
2025-08-25 20:50:14,050 - NotificationModule - INFO - Would send notification to user 'user_65': User 'user_54' you follow has published a new post 'post_b76c8f9f'.
2025-08-25 20:50:14,058 - Simulator - INFO - Added READ_POST event for user user_65 to queue. Queue size: 7
2025-08-25 20:50:14,058 - Simulator - INFO - Generated READ_POST event for user user_65 based on notification.
2025-08-25 20:50:14,058 - NotificationModule - INFO - Would send notification to user 'user_77': User 'user_54' you follow has published a new post 'post_b76c8f9f'.
2025-08-25 20:50:14,066 - Simulator - INFO - Added READ_POST event for user user_77 to queue. Queue size: 8
2025-08-25 20:50:14,066 - Simulator - INFO - Generated READ_POST event for user user_77 based on notification.
2025-08-25 20:50:14,066 - Engine - INFO - Sent new post notification to 8 followers.
2025-08-25 20:50:14,067 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_54'...
2025-08-25 20:50:14,067 - src.modules.belief_module - INFO - 开始处理用户 'user_54' 的认知流程...
2025-08-25 20:50:14,082 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:50:14,082 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:50:14,082 - src.modules.belief_module - INFO - 用户 'user_54' 的短期记忆中未找到显著议题簇
2025-08-25 20:50:14,082 - Engine - INFO - Cognitive processing for user 'user_54' finished.
2025-08-25 20:50:14,082 - Engine - INFO - ----- Action 'CREATE_POST' for user 'user_54' processed successfully. -----
2025-08-25 20:50:14,083 - Simulator - INFO - Processing event from queue: READ_POST for user user_13
2025-08-25 20:50:14,083 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_13' -----
2025-08-25 20:50:20,802 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_13_54dda292).
2025-08-25 20:50:20,802 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-25 20:50:23,865 - src.modules.content_module - INFO - Incremented view count for post 'post_b76c8f9f'.
2025-08-25 20:50:23,865 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:50:23,865 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:50:23,865 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_13'...
2025-08-25 20:50:23,865 - src.modules.belief_module - INFO - 开始处理用户 'user_13' 的认知流程...
2025-08-25 20:50:23,880 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:50:23,881 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:50:23,881 - src.modules.belief_module - INFO - 用户 'user_13' 的短期记忆中未找到显著议题簇
2025-08-25 20:50:23,881 - Engine - INFO - Cognitive processing for user 'user_13' finished.
2025-08-25 20:50:23,881 - Engine - INFO - ----- Action 'READ_POST' for user 'user_13' processed successfully. -----
【处理事件】从队列中处理事件: READ_POST (用户 user_52)，浏览帖子 post_3："评论内容: 看到这个帖子，心里挺不是滋味的。如果真有这么一款能逆转糖尿病的平价药，那对多少人意味着希..."
【引擎】开始处理用户 user_52 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_52'...
MemoryModule: Successfully created memory 'mem_user_52_1734a87b'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_52_1734a87b)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_52 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_52 的认知处理...

【认知处理】开始处理用户 user_52 的认知流程
【阶段1+2】获取用户 user_52 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_52'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_52 的认知处理完成
【引擎】用户 user_52 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_52)

【模拟步骤】选择用户: user_54 执行操作 (步骤 4/5, 轮次 1/40)
【用户行为】用户 user_54 选择了 CREATE_POST 操作，内容："今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排财产继承是现实的智慧”，我真是笑出声了。智慧？这..."
【事件队列】添加事件: CREATE_POST (用户 user_54), 队列大小: 1
【处理事件】从队列中处理事件: CREATE_POST (用户 user_54)，发布内容："今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排财产继承是现实的智慧”，我真是笑出声了。智慧？这..."
【引擎】开始处理用户 user_54 的 CREATE_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_54'...
MemoryModule: Successfully created memory 'mem_user_54_f66a6a2d'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_54_f66a6a2d)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_54 的 CREATE_POST 操作是否需要发送通知...
【通知模块】用户 user_54 发布了新帖子，发送通知给关注者 user_13
【通知事件】生成通知事件: 用户 user_13 接收 READ_POST 通知
【通知事件】为用户 user_13 生成浏览事件，浏览帖子 post_b76c8f9f："今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排财产继承是现实的智慧”，我真是笑出声了。智慧？这..."
【事件队列】添加事件: READ_POST (用户 user_13), 队列大小: 1
【通知模块】用户 user_54 发布了新帖子，发送通知给关注者 user_84
【通知事件】生成通知事件: 用户 user_84 接收 READ_POST 通知
【通知事件】为用户 user_84 生成浏览事件，浏览帖子 post_b76c8f9f："今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排财产继承是现实的智慧”，我真是笑出声了。智慧？这..."
【事件队列】添加事件: READ_POST (用户 user_84), 队列大小: 2
【通知模块】用户 user_54 发布了新帖子，发送通知给关注者 user_34
【通知事件】生成通知事件: 用户 user_34 接收 READ_POST 通知
【通知事件】为用户 user_34 生成浏览事件，浏览帖子 post_b76c8f9f："今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排财产继承是现实的智慧”，我真是笑出声了。智慧？这..."
【事件队列】添加事件: READ_POST (用户 user_34), 队列大小: 3
【通知模块】用户 user_54 发布了新帖子，发送通知给关注者 user_42
【通知事件】生成通知事件: 用户 user_42 接收 READ_POST 通知
【通知事件】为用户 user_42 生成浏览事件，浏览帖子 post_b76c8f9f："今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排财产继承是现实的智慧”，我真是笑出声了。智慧？这..."
【事件队列】添加事件: READ_POST (用户 user_42), 队列大小: 4
【通知模块】用户 user_54 发布了新帖子，发送通知给关注者 user_67
【通知事件】生成通知事件: 用户 user_67 接收 READ_POST 通知
【通知事件】为用户 user_67 生成浏览事件，浏览帖子 post_b76c8f9f："今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排财产继承是现实的智慧”，我真是笑出声了。智慧？这..."
【事件队列】添加事件: READ_POST (用户 user_67), 队列大小: 5
【通知模块】用户 user_54 发布了新帖子，发送通知给关注者 user_93
【通知事件】生成通知事件: 用户 user_93 接收 READ_POST 通知
【通知事件】为用户 user_93 生成浏览事件，浏览帖子 post_b76c8f9f："今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排财产继承是现实的智慧”，我真是笑出声了。智慧？这..."
【事件队列】添加事件: READ_POST (用户 user_93), 队列大小: 6
【通知模块】用户 user_54 发布了新帖子，发送通知给关注者 user_65
【通知事件】生成通知事件: 用户 user_65 接收 READ_POST 通知
【通知事件】为用户 user_65 生成浏览事件，浏览帖子 post_b76c8f9f："今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排财产继承是现实的智慧”，我真是笑出声了。智慧？这..."
【事件队列】添加事件: READ_POST (用户 user_65), 队列大小: 7
【通知模块】用户 user_54 发布了新帖子，发送通知给关注者 user_77
【通知事件】生成通知事件: 用户 user_77 接收 READ_POST 通知
【通知事件】为用户 user_77 生成浏览事件，浏览帖子 post_b76c8f9f："今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排财产继承是现实的智慧”，我真是笑出声了。智慧？这..."
【事件队列】添加事件: READ_POST (用户 user_77), 队列大小: 8
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_54 的认知处理...

【认知处理】开始处理用户 user_54 的认知流程
【阶段1+2】获取用户 user_54 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_54'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_54 的认知处理完成
【引擎】用户 user_54 的 CREATE_POST 操作处理成功
【处理成功】事件处理成功: CREATE_POST (用户 user_54)
【处理事件】从队列中处理事件: READ_POST (用户 user_13)，浏览帖子 post_b76c8f9f："您关注的用户 user_54 发布了新帖子:

今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排..."
【引擎】开始处理用户 user_13 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_13'...
MemoryModule: Successfully created memory 'mem_user_13_54dda292'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_13_54dda292)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_13 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_13 的认知处理...

【认知处理】开始处理用户 user_13 的认知流程
【阶段1+2】获取用户 user_13 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_13'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_13 的认知处理完成
【引擎】用户 user_13 的 READ_POST 操作处理成功
2025-08-25 20:50:23,882 - Simulator - INFO - Processing event from queue: READ_POST for user user_84
2025-08-25 20:50:23,882 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_84' -----
2025-08-25 20:50:30,128 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_84_11280a0d).
2025-08-25 20:50:30,128 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-25 20:50:32,865 - src.modules.content_module - INFO - Incremented view count for post 'post_b76c8f9f'.
2025-08-25 20:50:32,865 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:50:32,865 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:50:32,865 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_84'...
2025-08-25 20:50:32,865 - src.modules.belief_module - INFO - 开始处理用户 'user_84' 的认知流程...
2025-08-25 20:50:32,881 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:50:32,881 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:50:32,881 - src.modules.belief_module - INFO - 用户 'user_84' 的短期记忆中未找到显著议题簇
2025-08-25 20:50:32,881 - Engine - INFO - Cognitive processing for user 'user_84' finished.
2025-08-25 20:50:32,881 - Engine - INFO - ----- Action 'READ_POST' for user 'user_84' processed successfully. -----
2025-08-25 20:50:32,882 - Simulator - INFO - Processing event from queue: READ_POST for user user_34
2025-08-25 20:50:32,882 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_34' -----
2025-08-25 20:50:39,970 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_34_e00e022a).
2025-08-25 20:50:39,970 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-25 20:50:42,682 - src.modules.content_module - INFO - Incremented view count for post 'post_b76c8f9f'.
2025-08-25 20:50:42,682 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:50:42,682 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:50:42,682 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_34'...
2025-08-25 20:50:42,682 - src.modules.belief_module - INFO - 开始处理用户 'user_34' 的认知流程...
2025-08-25 20:50:42,697 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:50:42,698 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:50:42,698 - src.modules.belief_module - INFO - 用户 'user_34' 的短期记忆中未找到显著议题簇
2025-08-25 20:50:42,698 - Engine - INFO - Cognitive processing for user 'user_34' finished.
2025-08-25 20:50:42,698 - Engine - INFO - ----- Action 'READ_POST' for user 'user_34' processed successfully. -----
2025-08-25 20:50:42,699 - Simulator - INFO - Processing event from queue: READ_POST for user user_42
2025-08-25 20:50:42,699 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_42' -----
2025-08-25 20:50:49,495 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_42_d9b94eaa).
2025-08-25 20:50:49,495 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-25 20:50:52,215 - src.modules.content_module - INFO - Incremented view count for post 'post_b76c8f9f'.
2025-08-25 20:50:52,215 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:50:52,216 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:50:52,216 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_42'...
2025-08-25 20:50:52,216 - src.modules.belief_module - INFO - 开始处理用户 'user_42' 的认知流程...
2025-08-25 20:50:52,231 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:50:52,231 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:50:52,231 - src.modules.belief_module - INFO - 用户 'user_42' 的短期记忆中未找到显著议题簇
2025-08-25 20:50:52,232 - Engine - INFO - Cognitive processing for user 'user_42' finished.
2025-08-25 20:50:52,232 - Engine - INFO - ----- Action 'READ_POST' for user 'user_42' processed successfully. -----
2025-08-25 20:50:52,232 - Simulator - INFO - Processing event from queue: READ_POST for user user_67
2025-08-25 20:50:52,233 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_67' -----
2025-08-25 20:50:59,710 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_67_43ae913b).
2025-08-25 20:50:59,710 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-25 20:51:04,848 - src.modules.content_module - INFO - Incremented view count for post 'post_b76c8f9f'.
2025-08-25 20:51:04,848 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:51:04,848 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:51:04,848 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_67'...
2025-08-25 20:51:04,848 - src.modules.belief_module - INFO - 开始处理用户 'user_67' 的认知流程...
2025-08-25 20:51:04,863 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:51:04,864 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:51:04,864 - src.modules.belief_module - INFO - 用户 'user_67' 的短期记忆中未找到显著议题簇
2025-08-25 20:51:04,864 - Engine - INFO - Cognitive processing for user 'user_67' finished.
2025-08-25 20:51:04,864 - Engine - INFO - ----- Action 'READ_POST' for user 'user_67' processed successfully. -----
2025-08-25 20:51:04,865 - Simulator - INFO - Processing event from queue: READ_POST for user user_93
2025-08-25 20:51:04,865 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_93' -----
2025-08-25 20:51:12,256 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_93_53ac71c4).
2025-08-25 20:51:12,257 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-25 20:51:15,102 - src.modules.content_module - INFO - Incremented view count for post 'post_b76c8f9f'.
2025-08-25 20:51:15,102 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:51:15,102 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:51:15,103 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_93'...
2025-08-25 20:51:15,103 - src.modules.belief_module - INFO - 开始处理用户 'user_93' 的认知流程...
2025-08-25 20:51:15,118 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:51:15,118 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:51:15,118 - src.modules.belief_module - INFO - 用户 'user_93' 的短期记忆中未找到显著议题簇
2025-08-25 20:51:15,118 - Engine - INFO - Cognitive processing for user 'user_93' finished.
2025-08-25 20:51:15,118 - Engine - INFO - ----- Action 'READ_POST' for user 'user_93' processed successfully. -----
2025-08-25 20:51:15,119 - Simulator - INFO - Processing event from queue: READ_POST for user user_65
2025-08-25 20:51:15,119 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_65' -----
2025-08-25 20:51:22,415 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_65_843eea8e).
2025-08-25 20:51:22,416 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-25 20:51:25,178 - src.modules.content_module - INFO - Incremented view count for post 'post_b76c8f9f'.
2025-08-25 20:51:25,178 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:51:25,178 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:51:25,178 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_65'...
2025-08-25 20:51:25,178 - src.modules.belief_module - INFO - 开始处理用户 'user_65' 的认知流程...
2025-08-25 20:51:25,193 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:51:25,193 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:51:25,193 - src.modules.belief_module - INFO - 用户 'user_65' 的短期记忆中未找到显著议题簇
2025-08-25 20:51:25,194 - Engine - INFO - Cognitive processing for user 'user_65' finished.
2025-08-25 20:51:25,194 - Engine - INFO - ----- Action 'READ_POST' for user 'user_65' processed successfully. -----
2025-08-25 20:51:25,194 - Simulator - INFO - Processing event from queue: READ_POST for user user_77
【处理成功】事件处理成功: READ_POST (用户 user_13)
【处理事件】从队列中处理事件: READ_POST (用户 user_84)，浏览帖子 post_b76c8f9f："您关注的用户 user_54 发布了新帖子:

今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排..."
【引擎】开始处理用户 user_84 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_84'...
MemoryModule: Successfully created memory 'mem_user_84_11280a0d'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_84_11280a0d)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_84 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_84 的认知处理...

【认知处理】开始处理用户 user_84 的认知流程
【阶段1+2】获取用户 user_84 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_84'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_84 的认知处理完成
【引擎】用户 user_84 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_84)
【处理事件】从队列中处理事件: READ_POST (用户 user_34)，浏览帖子 post_b76c8f9f："您关注的用户 user_54 发布了新帖子:

今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排..."
【引擎】开始处理用户 user_34 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_34'...
MemoryModule: Successfully created memory 'mem_user_34_e00e022a'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_34_e00e022a)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_34 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_34 的认知处理...

【认知处理】开始处理用户 user_34 的认知流程
【阶段1+2】获取用户 user_34 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_34'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_34 的认知处理完成
【引擎】用户 user_34 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_34)
【处理事件】从队列中处理事件: READ_POST (用户 user_42)，浏览帖子 post_b76c8f9f："您关注的用户 user_54 发布了新帖子:

今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排..."
【引擎】开始处理用户 user_42 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_42'...
MemoryModule: Successfully created memory 'mem_user_42_d9b94eaa'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_42_d9b94eaa)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_42 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_42 的认知处理...

【认知处理】开始处理用户 user_42 的认知流程
【阶段1+2】获取用户 user_42 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_42'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_42 的认知处理完成
【引擎】用户 user_42 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_42)
【处理事件】从队列中处理事件: READ_POST (用户 user_67)，浏览帖子 post_b76c8f9f："您关注的用户 user_54 发布了新帖子:

今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排..."
【引擎】开始处理用户 user_67 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_67'...
MemoryModule: Successfully created memory 'mem_user_67_43ae913b'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_67_43ae913b)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_67 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_67 的认知处理...

【认知处理】开始处理用户 user_67 的认知流程
【阶段1+2】获取用户 user_67 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_67'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_67 的认知处理完成
【引擎】用户 user_67 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_67)
【处理事件】从队列中处理事件: READ_POST (用户 user_93)，浏览帖子 post_b76c8f9f："您关注的用户 user_54 发布了新帖子:

今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排..."
【引擎】开始处理用户 user_93 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_93'...
MemoryModule: Successfully created memory 'mem_user_93_53ac71c4'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_93_53ac71c4)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_93 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_93 的认知处理...

【认知处理】开始处理用户 user_93 的认知流程
【阶段1+2】获取用户 user_93 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_93'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_93 的认知处理完成
【引擎】用户 user_93 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_93)
【处理事件】从队列中处理事件: READ_POST (用户 user_65)，浏览帖子 post_b76c8f9f："您关注的用户 user_54 发布了新帖子:

今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排..."
【引擎】开始处理用户 user_65 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_65'...
MemoryModule: Successfully created memory 'mem_user_65_843eea8e'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_65_843eea8e)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_65 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_65 的认知处理...

【认知处理】开始处理用户 user_65 的认知流程
【阶段1+2】获取用户 user_65 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_65'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_65 的认知处理完成
【引擎】用户 user_65 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_65)
【处理事件】从队列中处理事件: READ_POST (用户 user_77)，浏览帖子 post_b76c8f9f："您关注的用户 user_54 发布了新帖子:

今天刷到一个讨论婚姻和继承的视频，说“通过婚姻来安排..."
2025-08-25 20:51:25,194 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_77' -----
2025-08-25 20:51:32,182 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_77_dad74c59).
2025-08-25 20:51:32,182 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-25 20:51:35,011 - src.modules.content_module - INFO - Incremented view count for post 'post_b76c8f9f'.
2025-08-25 20:51:35,011 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:51:35,011 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:51:35,011 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_77'...
2025-08-25 20:51:35,011 - src.modules.belief_module - INFO - 开始处理用户 'user_77' 的认知流程...
2025-08-25 20:51:35,026 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:51:35,026 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:51:35,026 - src.modules.belief_module - INFO - 用户 'user_77' 的短期记忆中未找到显著议题簇
2025-08-25 20:51:35,027 - Engine - INFO - Cognitive processing for user 'user_77' finished.
2025-08-25 20:51:35,027 - Engine - INFO - ----- Action 'READ_POST' for user 'user_77' processed successfully. -----
2025-08-25 20:51:35,027 - Simulator - INFO - Simulation step completed for user user_54.
2025-08-25 20:51:35,027 - Simulator - INFO - Simulation step 5/5
2025-08-25 20:51:35,309 - Simulator - WARNING - 用户 user_84 没有信念，使用默认信念ID
2025-08-25 20:51:36,602 - Simulator - INFO - Added CREATE_COMMENT event for user user_84 to queue. Queue size: 1
2025-08-25 20:51:36,602 - Simulator - INFO - Processing event from queue: CREATE_COMMENT for user user_84
2025-08-25 20:51:36,602 - Engine - INFO - ----- Processing action 'CREATE_COMMENT' for user 'user_84' -----
2025-08-25 20:51:42,865 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_84_35c701ad).
2025-08-25 20:51:42,865 - Engine - INFO - Step 2/4: Updating content state for action 'CREATE_COMMENT'...
2025-08-25 20:51:48,374 - src.modules.content_module - INFO - Incremented comment count for post 'post_1'.
2025-08-25 20:51:50,977 - src.modules.agent_module - INFO - 用户 'user_84' 正在 关注 帖子 'post_1'...
2025-08-25 20:51:54,210 - src.modules.agent_module - INFO - 成功处理用户 'user_84' 关注 帖子 'post_1' 的操作。
2025-08-25 20:51:54,210 - src.modules.content_module - INFO - User 'user_84' successfully followed post 'post_1'.
2025-08-25 20:51:54,210 - src.modules.content_module - INFO - Added comment 'comment_a7c875b5' to post 'post_1' by user 'user_84'
2025-08-25 20:51:54,210 - src.modules.agent_module - INFO - 根据ID推断，正在为用户 'user_84' 的 'comments' 列表添加 ID 'comment_a7c875b5'...
2025-08-25 20:51:57,232 - src.modules.agent_module - INFO - 成功为用户 'user_84' 添加活动。
2025-08-25 20:51:57,232 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:51:57,235 - NotificationModule - INFO - Would send notification to user 'user_43': Your post 'post_1' was commented on by user 'user_84'.
2025-08-25 20:51:57,243 - Simulator - INFO - Added READ_POST event for user user_43 to queue. Queue size: 1
2025-08-25 20:51:57,243 - Simulator - INFO - Generated READ_POST event for user user_43 based on notification.
2025-08-25 20:51:57,243 - Engine - INFO - Comment notification sent to post author.
2025-08-25 20:51:57,248 - Engine - INFO - Sent new comment notification to 1 post followers.
2025-08-25 20:51:57,248 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_84'...
2025-08-25 20:51:57,248 - src.modules.belief_module - INFO - 开始处理用户 'user_84' 的认知流程...
2025-08-25 20:51:57,274 - src.modules.belief_module - INFO - 正在对 2 条短期记忆进行聚类分析...
2025-08-25 20:51:57,274 - src.modules.belief_module - INFO - 有效记忆数量为2，直接作为单个簇处理
2025-08-25 20:51:57,280 - src.modules.belief_module - INFO - 找到 1 个显著议题簇
2025-08-25 20:51:57,280 - src.modules.belief_module - INFO - 正在从记忆簇 'cluster_2f11e55a' 更新或创建信念...
2025-08-25 20:51:57,284 - src.modules.belief_module - INFO - 用户 'user_84' 没有任何信念，返回第一个记忆命题
2025-08-25 20:51:57,284 - src.modules.belief_module - INFO - 选择的备选信念命题: '这事儿有点悬，甘平宁听着像特效药，但“冒死爆料”太夸张了。要是真有大成果，瑞康生物早该走正规流程了，现在网上这么一闹，啥数据都没见着，来源也不清楚，容易被带节奏。别急着信，等官方或权威媒体出来说明再说。'
2025-08-25 20:51:57,284 - src.modules.belief_module - INFO - embedding相似度 (0.0000) 低于阈值 (0.9)，创建节点
2025-08-25 20:51:57,284 - src.modules.belief_module - INFO - 使用多线程模式聚合证据
2025-08-25 20:51:57,284 - src.modules.belief_module - INFO - 正在聚合记忆簇 'cluster_2f11e55a' 的证据（多线程模式）...
2025-08-25 20:52:01,358 - src.modules.belief_module - INFO - 聚合结果（多线程模式） - 支持权重: 0.5932, 反对权重: 0.0000
2025-08-25 20:52:01,358 - src.modules.belief_module - INFO - 正在为用户 'user_84' 创建新信念: '这事儿有点悬，甘平宁听着像特效药，但“冒死爆料”太夸张了。要...'
2025-08-25 20:52:06,204 - src.modules.belief_module - INFO - 使用多线程模式建立信念关系
2025-08-25 20:52:06,205 - src.modules.belief_module - INFO - 正在为信念 'belief_user_84_b641b4d7' 建立关系网络（多线程模式）...
2025-08-25 20:52:06,209 - src.modules.belief_module - INFO - 用户 'user_84' 没有其他信念，无需建立关系
2025-08-25 20:52:06,209 - src.modules.belief_module - INFO - 成功创建信念 'belief_user_84_b641b4d7':
2025-08-25 20:52:06,210 - src.modules.belief_module - INFO -   - 真实度: 0.5932
2025-08-25 20:52:06,210 - src.modules.belief_module - INFO -   - 置信度: 0.2825
2025-08-25 20:52:06,212 - src.modules.belief_module - INFO - 信念 'belief_user_84_b641b4d7' 的置信度 (0.2825) 低于求证阈值 (0.4900)
2025-08-25 20:52:06,213 - src.modules.belief_module - INFO - 正在为用户 'user_84' 触发对信念 'belief_user_84_b641b4d7' 的求证机制...
2025-08-25 20:52:06,213 - src.modules.belief_module - INFO - 从search.json中选择的搜索查询: 【深度辟谣】别再传“同居继承一半财产”了！律师告诉你真实情况是这样！
2025-08-25 20:52:06,213 - src.modules.belief_module - INFO - 搜索结果:
2025-08-25 20:52:06,214 - src.modules.belief_module - INFO - 1. 各位朋友，关于网传的“同居一年继承一半财产”的说法是彻头彻尾的谣言。根据我国《民法典》，同居伴侣不属于法定继承人。继承顺序第一位是配偶、子女、父母。除非逝者生前立下合法有效的遗嘱，明确将财产遗赠给同居伴侣，否则伴侣一分钱也继承不到。房本写谁名就是谁的，这叫不动产登记原则。别再被那些贩卖焦虑的谣言欺骗了！#法律科普 #辟谣 #继承法
2025-08-25 20:52:06,214 - src.modules.belief_module - INFO - 2. 这个谣言虽然是假的，但它能火爆全网，恰恰说明了一个现实问题：我国对非婚同居关系的法律保护确实存在空白。现实中，一方为家庭付出多年，但因没有一纸婚书，分手或一方离世后权益无法保障的案例比比皆是。我们不应止于辟谣，更应思考，如何在保护私有财产权和维护传统婚姻制度之间，为“事实伴侣”提供一条人道的、有限的救济途径？比如在分割同居期间共同财产（析产）或请求扶养补偿方面，是否可以有更明确的规定？#法律思考 #非婚同居 #民法典
2025-08-25 20:52:06,214 - src.modules.belief_module - INFO - 生成了求证搜索行为: '【深度辟谣】别再传“同居继承一半财产”了！律师告诉你真实情况是这样！'
2025-08-25 20:52:06,214 - src.modules.belief_module - INFO - 将求证搜索行为加入处理队列...
2025-08-25 20:52:06,214 - Engine - INFO - Added verification action to queue. Queue size: 1
2025-08-25 20:52:06,214 - src.modules.belief_module - INFO - 求证搜索行为已加入队列，将在当前认知处理完成后执行
2025-08-25 20:52:06,214 - src.modules.belief_module - INFO - 已触发信念求证, 查询: '【深度辟谣】别再传“同居继承一半财产”了！律师告诉你真实情况是这样！'
2025-08-25 20:52:06,214 - src.modules.belief_module - INFO - 认知冲击 (0.5932) 超过怀疑阈值 (0.3200)
2025-08-25 20:52:06,215 - src.modules.agent_module - INFO - 正在为用户 'user_84' 演化特质，认知冲击为: 0.5931643820477405, 内容情感强度: 0.5249999999999999
2025-08-25 20:52:06,217 - src.modules.agent_module - INFO - 基于内容情感强度 0.525 调整情绪波动性变化: 0.0024
2025-08-25 20:52:09,301 - src.modules.agent_module - INFO - 用户 'user_84' 的特质已更新:
2025-08-25 20:52:09,301 - src.modules.agent_module - INFO -   - 怀疑主义得分: 0.3793
2025-08-25 20:52:09,302 - src.modules.agent_module - INFO -   - 验证阈值: 0.5197
2025-08-25 20:52:09,302 - src.modules.agent_module - INFO -   - 情绪波动性: 0.6724
2025-08-25 20:52:09,302 - src.modules.belief_module - INFO - 正在传播信念 'belief_user_84_b641b4d7' 的变化，认知冲击为 0.5931643820477405...
2025-08-25 20:52:09,302 - src.modules.belief_module - INFO - 信念 'belief_user_84_b641b4d7' 没有关联边，无需传播
2025-08-25 20:52:09,302 - src.modules.belief_module - INFO - 已触发特质演化和信念传播, 冲击大小: 0.5932, 情感强度: 0.525
2025-08-25 20:52:09,302 - src.modules.belief_module - INFO - 将来自簇 cluster_2f11e55a 的 2 条记忆移入历史...
2025-08-25 20:52:20,887 - src.modules.belief_module - INFO - 成功移动 2/2 条记忆到历史存档
2025-08-25 20:52:20,888 - Engine - INFO - Cognitive processing for user 'user_84' finished.
2025-08-25 20:52:20,888 - Engine - INFO - Step 5/5: Processing verification queue with 1 actions...
2025-08-25 20:52:20,889 - Engine - INFO - Processing 1 verification actions from queue...
2025-08-25 20:52:20,889 - Engine - INFO - Processing queued verification action for user 'user_84'
2025-08-25 20:52:20,889 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_84' -----
2025-08-25 20:52:29,515 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_84_985602e8).
2025-08-25 20:52:29,515 - Engine - INFO - No target_id provided for a content update action that requires one.
2025-08-25 20:52:29,516 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:52:29,516 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:52:29,516 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_84'...
2025-08-25 20:52:29,516 - src.modules.belief_module - INFO - 开始处理用户 'user_84' 的认知流程...
2025-08-25 20:52:29,531 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:52:29,532 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:52:29,532 - src.modules.belief_module - INFO - 用户 'user_84' 的短期记忆中未找到显著议题簇
2025-08-25 20:52:29,532 - Engine - INFO - Cognitive processing for user 'user_84' finished.
2025-08-25 20:52:29,532 - Engine - INFO - ----- Action 'READ_POST' for user 'user_84' processed successfully. -----
2025-08-25 20:52:29,533 - Engine - INFO - Queued verification action processed successfully
2025-08-25 20:52:29,533 - Engine - INFO - All verification actions processed.
2025-08-25 20:52:29,533 - Engine - INFO - Verification queue processing finished.
2025-08-25 20:52:29,533 - Engine - INFO - ----- Action 'CREATE_COMMENT' for user 'user_84' processed successfully. -----
2025-08-25 20:52:29,533 - Simulator - INFO - Processing event from queue: READ_POST for user user_43
2025-08-25 20:52:29,533 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_43' -----
2025-08-25 20:52:36,087 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_43_8c8feb47).
2025-08-25 20:52:36,087 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-25 20:52:39,094 - src.modules.content_module - INFO - Incremented view count for post 'post_1'.
2025-08-25 20:52:39,094 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:52:39,094 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:52:39,095 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_43'...
2025-08-25 20:52:39,095 - src.modules.belief_module - INFO - 开始处理用户 'user_43' 的认知流程...
2025-08-25 20:52:39,110 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:52:39,110 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:52:39,110 - src.modules.belief_module - INFO - 用户 'user_43' 的短期记忆中未找到显著议题簇
2025-08-25 20:52:39,111 - Engine - INFO - Cognitive processing for user 'user_43' finished.
2025-08-25 20:52:39,111 - Engine - INFO - ----- Action 'READ_POST' for user 'user_43' processed successfully. -----
2025-08-25 20:52:39,111 - Simulator - INFO - Simulation step completed for user user_84.
2025-08-25 20:52:39,111 - Simulator - INFO - Simulation completed with 5 steps.
2025-08-25 20:52:41,348 - BeliefLogger - INFO - 已记录轮次 1 after 阶段的所有用户信念到 round_1_after_beliefs.json
2025-08-25 20:52:41,845 - BeliefLogger - INFO - 已记录轮次 1 after 阶段的所有用户记忆到 round_1_after_memories.json
2025-08-25 20:52:42,148 - BeliefLogger - INFO - 已记录轮次 1 after 阶段的所有用户信息到 round_1_after_users.json，共 100 个用户
2025-08-25 20:52:42,249 - ExperimentLoggers - INFO - 已记录轮次 1 after 阶段的所有帖子、评论和回复到 round_1_after_posts.json
2025-08-25 20:52:42,262 - ExperimentLoggers - INFO - 已记录轮次 1 after 阶段的所有评论和回复到 round_1_after_comments.json，共 36 条
2025-08-25 20:52:42,262 - ExperimentLogger - INFO - 开始记录轮次 1 after 阶段的实验指标...
2025-08-25 20:53:13,868 - ExperimentLogger - INFO - 已记录轮次 1 after 阶段的SIR指标到 round_1_after_sir_metrics.json
2025-08-25 20:53:13,869 - ExperimentLogger - INFO -   - 计算方法: 基于用户发言内容的谣言检测（符合SIR状态转换规则）
2025-08-25 20:53:13,869 - ExperimentLogger - INFO -   - 总体易感者: 89 (0.890) - 从未发布谣言内容
2025-08-25 20:53:13,869 - ExperimentLogger - INFO -   - 总体感染者: 11 (0.110) - 发布过谣言但未发布非谣言
2025-08-25 20:53:13,869 - ExperimentLogger - INFO -   - 总体康复者: 0 (0.000) - 曾经是感染者且后来发布了非谣言
2025-08-25 20:53:13,869 - ExperimentLogger - INFO -   - 知识分子群体SIR指标: 总数=30, 易感者=28(0.933), 感染者=2(0.067), 康复者=0(0.000)
2025-08-25 20:53:13,869 - ExperimentLogger - INFO -   - 普通群众群体SIR指标: 总数=70, 易感者=61(0.871), 感染者=9(0.129), 康复者=0(0.000)
2025-08-25 20:53:16,481 - ExperimentLogger - INFO - 已记录轮次 1 after 阶段的极化指数到 round_1_after_polarization_metrics.json
2025-08-25 20:53:16,481 - ExperimentLogger - INFO -   - 总体OEI指数: 0.570
2025-08-25 20:53:16,481 - ExperimentLogger - INFO -   - 极端观点用户: 57 (0.570)
2025-08-25 20:53:16,481 - ExperimentLogger - INFO -   - 温和观点用户: 24 (0.240)
2025-08-25 20:53:16,481 - ExperimentLogger - INFO -   - 无明确观点用户: 19 (0.190)
2025-08-25 20:53:16,481 - ExperimentLogger - INFO -   - 知识分子群体OEI指数: 1.000 (总数: 30, 极端: 30, 温和: 0, 无观点: 0)
2025-08-25 20:53:16,481 - ExperimentLogger - INFO -   - 普通群众群体OEI指数: 0.386 (总数: 70, 极端: 27, 温和: 24, 无观点: 19)
2025-08-25 20:53:19,385 - ExperimentLogger - INFO - 已记录轮次 1 after 阶段的智能体演化数据到 round_1_after_agent_evolution.json
2025-08-25 20:53:19,385 - ExperimentLogger - INFO -   - 追踪用户数: 100
2025-08-25 20:53:19,403 - ExperimentLogger - INFO - 已记录轮次 1 after 阶段的网络传播分析到 round_1_after_network_propagation.json
2025-08-25 20:53:19,403 - ExperimentLogger - INFO -   - 活跃用户数: 12
2025-08-25 20:53:19,403 - ExperimentLogger - INFO -   - 内容创作者数: 9
2025-08-25 20:53:19,403 - ExperimentLogger - INFO -   - 评论者数: 3
2025-08-25 20:53:19,403 - ExperimentLogger - INFO -   - 总帖子数: 11
2025-08-25 20:53:19,403 - ExperimentLogger - INFO -   - 总评论数: 36
2025-08-25 20:53:19,404 - ExperimentLogger - INFO - 轮次 1 after 阶段的实验指标记录完成
2025-08-25 20:53:19,412 - BeliefLogger - INFO - 已生成第 1 轮模拟的统计信息: round_1_summary.json
2025-08-25 20:53:19,417 - BeliefLogger - INFO - 已生成第 1 轮模拟的用户统计信息: round_1_users_summary.json
2025-08-25 20:53:19,418 - ExperimentLoggers - INFO - 已生成第 1 轮模拟的帖子统计信息: round_1_posts_summary.json
2025-08-25 20:53:19,418 - ExperimentLoggers - INFO - 已生成第 1 轮模拟的评论统计信息: round_1_comments_summary.json
2025-08-25 20:53:19,418 - Main - INFO - 第 1 轮模拟完成，信念、记忆和帖子已记录。
2025-08-25 20:53:19,418 - Main - INFO - 开始第 2/40 轮模拟
2025-08-25 20:53:21,668 - BeliefLogger - INFO - 已记录轮次 2 before 阶段的所有用户信念到 round_2_before_beliefs.json
2025-08-25 20:53:22,183 - BeliefLogger - INFO - 已记录轮次 2 before 阶段的所有用户记忆到 round_2_before_memories.json
2025-08-25 20:53:22,484 - BeliefLogger - INFO - 已记录轮次 2 before 阶段的所有用户信息到 round_2_before_users.json，共 100 个用户
2025-08-25 20:53:22,563 - ExperimentLoggers - INFO - 已记录轮次 2 before 阶段的所有帖子、评论和回复到 round_2_before_posts.json
2025-08-25 20:53:22,577 - ExperimentLoggers - INFO - 已记录轮次 2 before 阶段的所有评论和回复到 round_2_before_comments.json，共 36 条
2025-08-25 20:53:22,577 - ExperimentLogger - INFO - 跳过轮次 2 before 阶段的记录（与轮次 1 after 状态相同）
2025-08-25 20:53:22,577 - Simulator - INFO - 设置轮数信息: 当前轮次 2/40
2025-08-25 20:53:22,577 - Simulator - INFO - Starting simulation with 5 steps...
2025-08-25 20:53:22,577 - Simulator - INFO - Simulation step 1/5
2025-08-25 20:53:22,857 - Simulator - WARNING - 用户 user_53 没有信念，使用默认信念ID
2025-08-25 20:53:24,107 - Simulator - INFO - Added CREATE_COMMENT event for user user_53 to queue. Queue size: 1
2025-08-25 20:53:24,107 - Simulator - INFO - Processing event from queue: CREATE_COMMENT for user user_53
2025-08-25 20:53:24,107 - Engine - INFO - ----- Processing action 'CREATE_COMMENT' for user 'user_53' -----
2025-08-25 20:53:30,828 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_53_5c67e641).
2025-08-25 20:53:30,828 - Engine - INFO - Step 2/4: Updating content state for action 'CREATE_COMMENT'...
2025-08-25 20:53:36,477 - src.modules.content_module - INFO - Incremented comment count for post 'post_7'.
2025-08-25 20:53:39,324 - src.modules.agent_module - INFO - 用户 'user_53' 正在 关注 帖子 'post_7'...
2025-08-25 20:53:42,206 - src.modules.agent_module - INFO - 成功处理用户 'user_53' 关注 帖子 'post_7' 的操作。
2025-08-25 20:53:42,206 - src.modules.content_module - INFO - User 'user_53' successfully followed post 'post_7'.
2025-08-25 20:53:42,207 - src.modules.content_module - INFO - Added comment 'comment_1590c326' to post 'post_7' by user 'user_53'
2025-08-25 20:53:42,207 - src.modules.agent_module - INFO - 根据ID推断，正在为用户 'user_53' 的 'comments' 列表添加 ID 'comment_1590c326'...
2025-08-25 20:53:45,621 - src.modules.agent_module - INFO - 成功为用户 'user_53' 添加活动。
2025-08-25 20:53:45,621 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:53:45,624 - NotificationModule - INFO - Would send notification to user 'user_32': Your post 'post_7' was commented on by user 'user_53'.
2025-08-25 20:53:45,632 - Simulator - INFO - Added READ_POST event for user user_32 to queue. Queue size: 1
2025-08-25 20:53:45,632 - Simulator - INFO - Generated READ_POST event for user user_32 based on notification.
2025-08-25 20:53:45,632 - Engine - INFO - Comment notification sent to post author.
2025-08-25 20:53:45,637 - Engine - INFO - Sent new comment notification to 1 post followers.
【引擎】开始处理用户 user_77 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_77'...
MemoryModule: Successfully created memory 'mem_user_77_dad74c59'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_77_dad74c59)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_77 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_77 的认知处理...

【认知处理】开始处理用户 user_77 的认知流程
【阶段1+2】获取用户 user_77 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_77'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_77 的认知处理完成
【引擎】用户 user_77 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_77)

【模拟步骤】选择用户: user_84 执行操作 (步骤 5/5, 轮次 1/40)
Warning: Belief 'default_belief_id' not found.
【用户行为】用户 user_84 选择了 CREATE_COMMENT 操作，评论帖子 post_1："看到这帖子有点懵，甘平宁听起来像救命药，但“冒死爆料”这种说法太戏剧化了。瑞康生物要是真有这么牛的成..."
【事件队列】添加事件: CREATE_COMMENT (用户 user_84), 队列大小: 1
【处理事件】从队列中处理事件: CREATE_COMMENT (用户 user_84)，评论帖子 post_1："看到这帖子有点懵，甘平宁听起来像救命药，但“冒死爆料”这种说法太戏剧化了。瑞康生物要是真有这么牛的成..."
【引擎】开始处理用户 user_84 的 CREATE_COMMENT 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_84'...
MemoryModule: Successfully created memory 'mem_user_84_35c701ad'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_84_35c701ad)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_84 的 CREATE_COMMENT 操作是否需要发送通知...
【通知】发送评论通知给帖子作者 user_43，评论内容："看到这帖子有点懵，甘平宁听起来像救命药，但“冒死爆料”这种说法太戏剧化了。瑞康生物要是真有这么牛的成..."
【通知模块】用户 user_84 评论了帖子，发送通知给帖子作者 user_43
【通知事件】生成通知事件: 用户 user_43 接收 READ_POST 通知
【通知事件】为用户 user_43 生成浏览事件，浏览帖子 post_1："惊天内幕！有自称瑞康生物前研究员的人冒死爆料，该公司研发出一款名叫“甘平宁”的平价神药，临床试验显示..."
【事件队列】添加事件: READ_POST (用户 user_43), 队列大小: 1
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_84 的认知处理...

【认知处理】开始处理用户 user_84 的认知流程
【阶段1+2】获取用户 user_84 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_84'...
【阶段1+2】对短期记忆进行聚类分析
【阶段3】开始信念更新阶段，发现 1 个显著簇
【阶段3】处理第 1 个议题簇，包含 2 条记忆
【阶段3】正在更新或创建信念...
【阶段3】信念更新成功，信念ID: belief_user_84_b641b4d7
【步骤 4/4】完成: 用户 user_84 的认知处理完成
【步骤 5/5】处理求证队列，队列大小: 1
【引擎】开始处理用户 user_84 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_84'...
MemoryModule: Successfully created memory 'mem_user_84_985602e8'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_84_985602e8)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_84 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_84 的认知处理...

【认知处理】开始处理用户 user_84 的认知流程
【阶段1+2】获取用户 user_84 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_84'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_84 的认知处理完成
【引擎】用户 user_84 的 READ_POST 操作处理成功
【步骤 5/5】完成: 求证队列处理完成
【引擎】用户 user_84 的 CREATE_COMMENT 操作处理成功
【处理成功】事件处理成功: CREATE_COMMENT (用户 user_84)
【处理事件】从队列中处理事件: READ_POST (用户 user_43)，浏览帖子 post_1："评论内容: 看到这帖子有点懵，甘平宁听起来像救命药，但“冒死爆料”这种说法太戏剧化了。瑞康生物要是真..."
【引擎】开始处理用户 user_43 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_43'...
MemoryModule: Successfully created memory 'mem_user_43_8c8feb47'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_43_8c8feb47)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_43 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_43 的认知处理...

【认知处理】开始处理用户 user_43 的认知流程
【阶段1+2】获取用户 user_43 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_43'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_43 的认知处理完成
【引擎】用户 user_43 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_43)

【模拟步骤】选择用户: user_53 执行操作 (步骤 1/5, 轮次 2/40)
Warning: Belief 'default_belief_id' not found.
【用户行为】用户 user_53 选择了 CREATE_COMMENT 操作，评论帖子 post_7："看到这帖子有点无语，瑞康生物要是真发声明说“甘平宁”有未知长期风险，那也得有科学依据吧？现在就靠内部..."
【事件队列】添加事件: CREATE_COMMENT (用户 user_53), 队列大小: 1
【处理事件】从队列中处理事件: CREATE_COMMENT (用户 user_53)，评论帖子 post_7："看到这帖子有点无语，瑞康生物要是真发声明说“甘平宁”有未知长期风险，那也得有科学依据吧？现在就靠内部..."
【引擎】开始处理用户 user_53 的 CREATE_COMMENT 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_53'...
MemoryModule: Successfully created memory 'mem_user_53_5c67e641'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_53_5c67e641)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_53 的 CREATE_COMMENT 操作是否需要发送通知...
【通知】发送评论通知给帖子作者 user_32，评论内容："看到这帖子有点无语，瑞康生物要是真发声明说“甘平宁”有未知长期风险，那也得有科学依据吧？现在就靠内部..."
【通知模块】用户 user_53 评论了帖子，发送通知给帖子作者 user_32
【通知事件】生成通知事件: 用户 user_32 接收 READ_POST 通知
【通知事件】为用户 user_32 生成浏览事件，浏览帖子 post_7："最新洗白套路预测：瑞康生物很快会发声明，说叫停“甘平宁”是因为“存在未知的长期风险”。可笑！内部爆料..."
【事件队列】添加事件: READ_POST (用户 user_32), 队列大小: 1
【步骤 3/4】完成: 通知处理完成
2025-08-25 20:53:45,638 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_53'...
2025-08-25 20:53:45,638 - src.modules.belief_module - INFO - 开始处理用户 'user_53' 的认知流程...
2025-08-25 20:53:45,653 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:53:45,653 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:53:45,654 - src.modules.belief_module - INFO - 用户 'user_53' 的短期记忆中未找到显著议题簇
2025-08-25 20:53:45,654 - Engine - INFO - Cognitive processing for user 'user_53' finished.
2025-08-25 20:53:45,654 - Engine - INFO - ----- Action 'CREATE_COMMENT' for user 'user_53' processed successfully. -----
2025-08-25 20:53:45,654 - Simulator - INFO - Processing event from queue: READ_POST for user user_32
2025-08-25 20:53:45,655 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_32' -----
2025-08-25 20:53:52,518 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_32_eaf3e33c).
2025-08-25 20:53:52,518 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-25 20:53:55,669 - src.modules.content_module - INFO - Incremented view count for post 'post_7'.
2025-08-25 20:53:55,669 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:53:55,669 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:53:55,669 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_32'...
2025-08-25 20:53:55,669 - src.modules.belief_module - INFO - 开始处理用户 'user_32' 的认知流程...
2025-08-25 20:53:55,685 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:53:55,685 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:53:55,685 - src.modules.belief_module - INFO - 用户 'user_32' 的短期记忆中未找到显著议题簇
2025-08-25 20:53:55,685 - Engine - INFO - Cognitive processing for user 'user_32' finished.
2025-08-25 20:53:55,685 - Engine - INFO - ----- Action 'READ_POST' for user 'user_32' processed successfully. -----
2025-08-25 20:53:55,686 - Simulator - INFO - Simulation step completed for user user_53.
2025-08-25 20:53:55,686 - Simulator - INFO - Simulation step 2/5
2025-08-25 20:53:58,461 - Simulator - INFO - Added CREATE_POST event for user user_60 to queue. Queue size: 1
2025-08-25 20:53:58,461 - Simulator - INFO - Processing event from queue: CREATE_POST for user user_60
2025-08-25 20:53:58,462 - Engine - INFO - ----- Processing action 'CREATE_POST' for user 'user_60' -----
2025-08-25 20:54:05,612 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_60_4284cd0e).
2025-08-25 20:54:05,612 - Engine - INFO - Step 2/4: Updating content state for action 'CREATE_POST'...
2025-08-25 20:54:08,358 - src.modules.content_module - INFO - 帖子 'post_e60be3cd' 由用户 'user_60' 创建成功。
2025-08-25 20:54:08,358 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:54:08,361 - NotificationModule - INFO - Would send notification to user 'user_10': User 'user_60' you follow has published a new post 'post_e60be3cd'.
2025-08-25 20:54:08,369 - Simulator - INFO - Added READ_POST event for user user_10 to queue. Queue size: 1
2025-08-25 20:54:08,369 - Simulator - INFO - Generated READ_POST event for user user_10 based on notification.
2025-08-25 20:54:08,369 - NotificationModule - INFO - Would send notification to user 'user_42': User 'user_60' you follow has published a new post 'post_e60be3cd'.
2025-08-25 20:54:08,377 - Simulator - INFO - Added READ_POST event for user user_42 to queue. Queue size: 2
2025-08-25 20:54:08,377 - Simulator - INFO - Generated READ_POST event for user user_42 based on notification.
2025-08-25 20:54:08,377 - NotificationModule - INFO - Would send notification to user 'user_82': User 'user_60' you follow has published a new post 'post_e60be3cd'.
2025-08-25 20:54:08,385 - Simulator - INFO - Added READ_POST event for user user_82 to queue. Queue size: 3
2025-08-25 20:54:08,386 - Simulator - INFO - Generated READ_POST event for user user_82 based on notification.
2025-08-25 20:54:08,386 - NotificationModule - INFO - Would send notification to user 'user_65': User 'user_60' you follow has published a new post 'post_e60be3cd'.
2025-08-25 20:54:08,394 - Simulator - INFO - Added READ_POST event for user user_65 to queue. Queue size: 4
2025-08-25 20:54:08,394 - Simulator - INFO - Generated READ_POST event for user user_65 based on notification.
2025-08-25 20:54:08,394 - NotificationModule - INFO - Would send notification to user 'user_86': User 'user_60' you follow has published a new post 'post_e60be3cd'.
2025-08-25 20:54:08,402 - Simulator - INFO - Added READ_POST event for user user_86 to queue. Queue size: 5
2025-08-25 20:54:08,402 - Simulator - INFO - Generated READ_POST event for user user_86 based on notification.
2025-08-25 20:54:08,402 - NotificationModule - INFO - Would send notification to user 'user_63': User 'user_60' you follow has published a new post 'post_e60be3cd'.
2025-08-25 20:54:08,410 - Simulator - INFO - Added READ_POST event for user user_63 to queue. Queue size: 6
2025-08-25 20:54:08,410 - Simulator - INFO - Generated READ_POST event for user user_63 based on notification.
2025-08-25 20:54:08,410 - NotificationModule - INFO - Would send notification to user 'user_78': User 'user_60' you follow has published a new post 'post_e60be3cd'.
2025-08-25 20:54:08,418 - Simulator - INFO - Added READ_POST event for user user_78 to queue. Queue size: 7
2025-08-25 20:54:08,418 - Simulator - INFO - Generated READ_POST event for user user_78 based on notification.
2025-08-25 20:54:08,418 - NotificationModule - INFO - Would send notification to user 'user_51': User 'user_60' you follow has published a new post 'post_e60be3cd'.
2025-08-25 20:54:08,426 - Simulator - INFO - Added READ_POST event for user user_51 to queue. Queue size: 8
2025-08-25 20:54:08,426 - Simulator - INFO - Generated READ_POST event for user user_51 based on notification.
2025-08-25 20:54:08,426 - Engine - INFO - Sent new post notification to 8 followers.
2025-08-25 20:54:08,426 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_60'...
2025-08-25 20:54:08,427 - src.modules.belief_module - INFO - 开始处理用户 'user_60' 的认知流程...
2025-08-25 20:54:08,442 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:54:08,442 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:54:08,442 - src.modules.belief_module - INFO - 用户 'user_60' 的短期记忆中未找到显著议题簇
2025-08-25 20:54:08,443 - Engine - INFO - Cognitive processing for user 'user_60' finished.
2025-08-25 20:54:08,443 - Engine - INFO - ----- Action 'CREATE_POST' for user 'user_60' processed successfully. -----
2025-08-25 20:54:08,443 - Simulator - INFO - Processing event from queue: READ_POST for user user_10
2025-08-25 20:54:08,443 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_10' -----
2025-08-25 20:54:16,046 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_10_192149b8).
2025-08-25 20:54:16,047 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-25 20:54:21,238 - src.modules.content_module - INFO - Incremented view count for post 'post_e60be3cd'.
2025-08-25 20:54:21,238 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:54:21,238 - Engine - INFO - No notification needed for this action type.
【步骤 4/4】开始用户 user_53 的认知处理...

【认知处理】开始处理用户 user_53 的认知流程
【阶段1+2】获取用户 user_53 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_53'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_53 的认知处理完成
【引擎】用户 user_53 的 CREATE_COMMENT 操作处理成功
【处理成功】事件处理成功: CREATE_COMMENT (用户 user_53)
【处理事件】从队列中处理事件: READ_POST (用户 user_32)，浏览帖子 post_7："评论内容: 看到这帖子有点无语，瑞康生物要是真发声明说“甘平宁”有未知长期风险，那也得有科学依据吧？..."
【引擎】开始处理用户 user_32 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_32'...
MemoryModule: Successfully created memory 'mem_user_32_eaf3e33c'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_32_eaf3e33c)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_32 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_32 的认知处理...

【认知处理】开始处理用户 user_32 的认知流程
【阶段1+2】获取用户 user_32 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_32'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_32 的认知处理完成
【引擎】用户 user_32 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_32)

【模拟步骤】选择用户: user_60 执行操作 (步骤 2/5, 轮次 2/40)
【用户行为】用户 user_60 选择了 CREATE_POST 操作，内容："最近刷到一堆关于婚姻和继承的分析，什么“离婚率上升”“财产分割比例”“遗嘱覆盖率”……数据堆得跟山一..."
【事件队列】添加事件: CREATE_POST (用户 user_60), 队列大小: 1
【处理事件】从队列中处理事件: CREATE_POST (用户 user_60)，发布内容："最近刷到一堆关于婚姻和继承的分析，什么“离婚率上升”“财产分割比例”“遗嘱覆盖率”……数据堆得跟山一..."
【引擎】开始处理用户 user_60 的 CREATE_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_60'...
MemoryModule: Successfully created memory 'mem_user_60_4284cd0e'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_60_4284cd0e)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_60 的 CREATE_POST 操作是否需要发送通知...
【通知模块】用户 user_60 发布了新帖子，发送通知给关注者 user_10
【通知事件】生成通知事件: 用户 user_10 接收 READ_POST 通知
【通知事件】为用户 user_10 生成浏览事件，浏览帖子 post_e60be3cd："最近刷到一堆关于婚姻和继承的分析，什么“离婚率上升”“财产分割比例”“遗嘱覆盖率”……数据堆得跟山一..."
【事件队列】添加事件: READ_POST (用户 user_10), 队列大小: 1
【通知模块】用户 user_60 发布了新帖子，发送通知给关注者 user_42
【通知事件】生成通知事件: 用户 user_42 接收 READ_POST 通知
【通知事件】为用户 user_42 生成浏览事件，浏览帖子 post_e60be3cd："最近刷到一堆关于婚姻和继承的分析，什么“离婚率上升”“财产分割比例”“遗嘱覆盖率”……数据堆得跟山一..."
【事件队列】添加事件: READ_POST (用户 user_42), 队列大小: 2
【通知模块】用户 user_60 发布了新帖子，发送通知给关注者 user_82
【通知事件】生成通知事件: 用户 user_82 接收 READ_POST 通知
【通知事件】为用户 user_82 生成浏览事件，浏览帖子 post_e60be3cd："最近刷到一堆关于婚姻和继承的分析，什么“离婚率上升”“财产分割比例”“遗嘱覆盖率”……数据堆得跟山一..."
【事件队列】添加事件: READ_POST (用户 user_82), 队列大小: 3
【通知模块】用户 user_60 发布了新帖子，发送通知给关注者 user_65
【通知事件】生成通知事件: 用户 user_65 接收 READ_POST 通知
【通知事件】为用户 user_65 生成浏览事件，浏览帖子 post_e60be3cd："最近刷到一堆关于婚姻和继承的分析，什么“离婚率上升”“财产分割比例”“遗嘱覆盖率”……数据堆得跟山一..."
【事件队列】添加事件: READ_POST (用户 user_65), 队列大小: 4
【通知模块】用户 user_60 发布了新帖子，发送通知给关注者 user_86
【通知事件】生成通知事件: 用户 user_86 接收 READ_POST 通知
【通知事件】为用户 user_86 生成浏览事件，浏览帖子 post_e60be3cd："最近刷到一堆关于婚姻和继承的分析，什么“离婚率上升”“财产分割比例”“遗嘱覆盖率”……数据堆得跟山一..."
【事件队列】添加事件: READ_POST (用户 user_86), 队列大小: 5
【通知模块】用户 user_60 发布了新帖子，发送通知给关注者 user_63
【通知事件】生成通知事件: 用户 user_63 接收 READ_POST 通知
【通知事件】为用户 user_63 生成浏览事件，浏览帖子 post_e60be3cd："最近刷到一堆关于婚姻和继承的分析，什么“离婚率上升”“财产分割比例”“遗嘱覆盖率”……数据堆得跟山一..."
【事件队列】添加事件: READ_POST (用户 user_63), 队列大小: 6
【通知模块】用户 user_60 发布了新帖子，发送通知给关注者 user_78
【通知事件】生成通知事件: 用户 user_78 接收 READ_POST 通知
【通知事件】为用户 user_78 生成浏览事件，浏览帖子 post_e60be3cd："最近刷到一堆关于婚姻和继承的分析，什么“离婚率上升”“财产分割比例”“遗嘱覆盖率”……数据堆得跟山一..."
【事件队列】添加事件: READ_POST (用户 user_78), 队列大小: 7
【通知模块】用户 user_60 发布了新帖子，发送通知给关注者 user_51
【通知事件】生成通知事件: 用户 user_51 接收 READ_POST 通知
【通知事件】为用户 user_51 生成浏览事件，浏览帖子 post_e60be3cd："最近刷到一堆关于婚姻和继承的分析，什么“离婚率上升”“财产分割比例”“遗嘱覆盖率”……数据堆得跟山一..."
【事件队列】添加事件: READ_POST (用户 user_51), 队列大小: 8
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_60 的认知处理...

【认知处理】开始处理用户 user_60 的认知流程
【阶段1+2】获取用户 user_60 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_60'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_60 的认知处理完成
【引擎】用户 user_60 的 CREATE_POST 操作处理成功
【处理成功】事件处理成功: CREATE_POST (用户 user_60)
【处理事件】从队列中处理事件: READ_POST (用户 user_10)，浏览帖子 post_e60be3cd："您关注的用户 user_60 发布了新帖子:

最近刷到一堆关于婚姻和继承的分析，什么“离婚率上升”..."
【引擎】开始处理用户 user_10 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_10'...
MemoryModule: Successfully created memory 'mem_user_10_192149b8'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_10_192149b8)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_10 的 READ_POST 操作是否需要发送通知...
2025-08-25 20:54:21,238 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_10'...
2025-08-25 20:54:21,238 - src.modules.belief_module - INFO - 开始处理用户 'user_10' 的认知流程...
2025-08-25 20:54:21,254 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:54:21,254 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:54:21,254 - src.modules.belief_module - INFO - 用户 'user_10' 的短期记忆中未找到显著议题簇
2025-08-25 20:54:21,254 - Engine - INFO - Cognitive processing for user 'user_10' finished.
2025-08-25 20:54:21,254 - Engine - INFO - ----- Action 'READ_POST' for user 'user_10' processed successfully. -----
2025-08-25 20:54:21,255 - Simulator - INFO - Processing event from queue: READ_POST for user user_42
2025-08-25 20:54:21,255 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_42' -----
2025-08-25 20:54:28,873 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_42_2e4eccd0).
2025-08-25 20:54:28,873 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-25 20:54:31,711 - src.modules.content_module - INFO - Incremented view count for post 'post_e60be3cd'.
2025-08-25 20:54:31,711 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:54:31,712 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:54:31,712 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_42'...
2025-08-25 20:54:31,712 - src.modules.belief_module - INFO - 开始处理用户 'user_42' 的认知流程...
2025-08-25 20:54:31,738 - src.modules.belief_module - INFO - 正在对 2 条短期记忆进行聚类分析...
2025-08-25 20:54:31,738 - src.modules.belief_module - INFO - 有效记忆数量为2，直接作为单个簇处理
2025-08-25 20:54:31,743 - src.modules.belief_module - INFO - 找到 1 个显著议题簇
2025-08-25 20:54:31,744 - src.modules.belief_module - INFO - 正在从记忆簇 'cluster_2564fbd1' 更新或创建信念...
2025-08-25 20:54:31,747 - src.modules.belief_module - INFO - 用户 'user_42' 没有任何信念，返回第一个记忆命题
2025-08-25 20:54:31,748 - src.modules.belief_module - INFO - 选择的备选信念命题: '最近看到一堆关于婚姻和继承的数据分析，什么离婚率、财产分割、遗嘱比例，看得人头大。但这些数字根本说不清王姨为啥守着老房子十年，也讲不明白李叔塞存折时那颤抖的手有多重。我邻居老张中风，儿子儿媳轮流照顾，没签协议，也没算继承顺序，可每天端汤送药，比啥法律都实在。我信的不是数字，是人和人之间的那份温度。再清楚的数据，也比不上你亲眼看见一个人在病床前默默擦汗的样子。说到底，婚姻和继承，是人，不是账本。'
2025-08-25 20:54:31,748 - src.modules.belief_module - INFO - embedding相似度 (0.0000) 低于阈值 (0.9)，创建节点
2025-08-25 20:54:31,748 - src.modules.belief_module - INFO - 使用多线程模式聚合证据
2025-08-25 20:54:31,748 - src.modules.belief_module - INFO - 正在聚合记忆簇 'cluster_2564fbd1' 的证据（多线程模式）...
2025-08-25 20:54:38,352 - src.modules.belief_module - INFO - 聚合结果（多线程模式） - 支持权重: 0.6155, 反对权重: 0.0000
2025-08-25 20:54:38,352 - src.modules.belief_module - INFO - 正在为用户 'user_42' 创建新信念: '最近看到一堆关于婚姻和继承的数据分析，什么离婚率、财产分割、...'
2025-08-25 20:54:41,777 - src.modules.belief_module - INFO - 使用多线程模式建立信念关系
2025-08-25 20:54:41,777 - src.modules.belief_module - INFO - 正在为信念 'belief_user_42_50fe1724' 建立关系网络（多线程模式）...
2025-08-25 20:54:41,781 - src.modules.belief_module - INFO - 用户 'user_42' 没有其他信念，无需建立关系
2025-08-25 20:54:41,781 - src.modules.belief_module - INFO - 成功创建信念 'belief_user_42_50fe1724':
2025-08-25 20:54:41,781 - src.modules.belief_module - INFO -   - 真实度: 0.6155
2025-08-25 20:54:41,781 - src.modules.belief_module - INFO -   - 置信度: 0.2932
2025-08-25 20:54:41,783 - src.modules.belief_module - INFO - 信念 'belief_user_42_50fe1724' 的置信度 (0.2932) 低于求证阈值 (0.3600)
2025-08-25 20:54:41,784 - src.modules.belief_module - INFO - 正在为用户 'user_42' 触发对信念 'belief_user_42_50fe1724' 的求证机制...
2025-08-25 20:54:41,784 - src.modules.belief_module - INFO - 从search.json中选择的搜索查询: 【深度辟谣】别再传“同居继承一半财产”了！律师告诉你真实情况是这样！
2025-08-25 20:54:41,784 - src.modules.belief_module - INFO - 搜索结果:
2025-08-25 20:54:41,784 - src.modules.belief_module - INFO - 1. 各位朋友，关于网传的“同居一年继承一半财产”的说法是彻头彻尾的谣言。根据我国《民法典》，同居伴侣不属于法定继承人。继承顺序第一位是配偶、子女、父母。除非逝者生前立下合法有效的遗嘱，明确将财产遗赠给同居伴侣，否则伴侣一分钱也继承不到。房本写谁名就是谁的，这叫不动产登记原则。别再被那些贩卖焦虑的谣言欺骗了！#法律科普 #辟谣 #继承法
2025-08-25 20:54:41,784 - src.modules.belief_module - INFO - 2. 大家别慌，我们国家的法律基石是很稳固的。关于财产，尤其是房子这种大事，核心就一条：不动产登记簿上写谁，就是谁的。这叫物权公示公信原则。别说同居一年，就是同居十年，只要房本没你名，法律上你就没份。所有让你焦虑的说法，都绕不开这个基本原则。所以，守好你的产权证，比什么都强。#物权法 #不动产登记 #硬核知识
2025-08-25 20:54:41,784 - src.modules.belief_module - INFO - 生成了求证搜索行为: '【深度辟谣】别再传“同居继承一半财产”了！律师告诉你真实情况是这样！'
2025-08-25 20:54:41,784 - src.modules.belief_module - INFO - 将求证搜索行为加入处理队列...
2025-08-25 20:54:41,784 - Engine - INFO - Added verification action to queue. Queue size: 1
2025-08-25 20:54:41,784 - src.modules.belief_module - INFO - 求证搜索行为已加入队列，将在当前认知处理完成后执行
2025-08-25 20:54:41,784 - src.modules.belief_module - INFO - 已触发信念求证, 查询: '【深度辟谣】别再传“同居继承一半财产”了！律师告诉你真实情况是这样！'
2025-08-25 20:54:41,784 - src.modules.belief_module - INFO - 认知冲击 (0.6155) 超过怀疑阈值 (0.2900)
2025-08-25 20:54:41,784 - src.modules.agent_module - INFO - 正在为用户 'user_42' 演化特质，认知冲击为: 0.6155214143597626, 内容情感强度: 0.65625
2025-08-25 20:54:41,787 - src.modules.agent_module - INFO - 基于内容情感强度 0.656 调整情绪波动性变化: 0.0154
2025-08-25 20:54:45,093 - src.modules.agent_module - INFO - 用户 'user_42' 的特质已更新:
2025-08-25 20:54:45,094 - src.modules.agent_module - INFO -   - 怀疑主义得分: 0.3516
2025-08-25 20:54:45,094 - src.modules.agent_module - INFO -   - 验证阈值: 0.3908
2025-08-25 20:54:45,094 - src.modules.agent_module - INFO -   - 情绪波动性: 0.8054
2025-08-25 20:54:45,094 - src.modules.belief_module - INFO - 正在传播信念 'belief_user_42_50fe1724' 的变化，认知冲击为 0.6155214143597626...
2025-08-25 20:54:45,094 - src.modules.belief_module - INFO - 信念 'belief_user_42_50fe1724' 没有关联边，无需传播
2025-08-25 20:54:45,094 - src.modules.belief_module - INFO - 已触发特质演化和信念传播, 冲击大小: 0.6155, 情感强度: 0.656
2025-08-25 20:54:45,094 - src.modules.belief_module - INFO - 将来自簇 cluster_2564fbd1 的 2 条记忆移入历史...
2025-08-25 20:54:56,976 - src.modules.belief_module - INFO - 成功移动 2/2 条记忆到历史存档
2025-08-25 20:54:56,978 - Engine - INFO - Cognitive processing for user 'user_42' finished.
2025-08-25 20:54:56,978 - Engine - INFO - Step 5/5: Processing verification queue with 1 actions...
2025-08-25 20:54:56,978 - Engine - INFO - Processing 1 verification actions from queue...
2025-08-25 20:54:56,978 - Engine - INFO - Processing queued verification action for user 'user_42'
2025-08-25 20:54:56,978 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_42' -----
2025-08-25 20:55:03,657 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_42_eb69fc7f).
2025-08-25 20:55:03,657 - Engine - INFO - No target_id provided for a content update action that requires one.
2025-08-25 20:55:03,657 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:55:03,658 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:55:03,658 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_42'...
2025-08-25 20:55:03,658 - src.modules.belief_module - INFO - 开始处理用户 'user_42' 的认知流程...
2025-08-25 20:55:03,668 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:55:03,668 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:55:03,668 - src.modules.belief_module - INFO - 用户 'user_42' 的短期记忆中未找到显著议题簇
2025-08-25 20:55:03,668 - Engine - INFO - Cognitive processing for user 'user_42' finished.
2025-08-25 20:55:03,668 - Engine - INFO - ----- Action 'READ_POST' for user 'user_42' processed successfully. -----
2025-08-25 20:55:03,668 - Engine - INFO - Queued verification action processed successfully
2025-08-25 20:55:03,668 - Engine - INFO - All verification actions processed.
2025-08-25 20:55:03,669 - Engine - INFO - Verification queue processing finished.
2025-08-25 20:55:03,669 - Engine - INFO - ----- Action 'READ_POST' for user 'user_42' processed successfully. -----
2025-08-25 20:55:03,669 - Simulator - INFO - Processing event from queue: READ_POST for user user_82
2025-08-25 20:55:03,669 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_82' -----
2025-08-25 20:55:10,880 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_82_a2b8c611).
2025-08-25 20:55:10,880 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-25 20:55:14,019 - src.modules.content_module - INFO - Incremented view count for post 'post_e60be3cd'.
2025-08-25 20:55:14,019 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:55:14,019 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:55:14,020 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_82'...
2025-08-25 20:55:14,020 - src.modules.belief_module - INFO - 开始处理用户 'user_82' 的认知流程...
2025-08-25 20:55:14,035 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-25 20:55:14,035 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-25 20:55:14,035 - src.modules.belief_module - INFO - 用户 'user_82' 的短期记忆中未找到显著议题簇
2025-08-25 20:55:14,035 - Engine - INFO - Cognitive processing for user 'user_82' finished.
2025-08-25 20:55:14,035 - Engine - INFO - ----- Action 'READ_POST' for user 'user_82' processed successfully. -----
2025-08-25 20:55:14,036 - Simulator - INFO - Processing event from queue: READ_POST for user user_65
2025-08-25 20:55:14,036 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_65' -----
2025-08-25 20:55:21,152 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_65_6c5e748f).
2025-08-25 20:55:21,152 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-25 20:55:24,115 - src.modules.content_module - INFO - Incremented view count for post 'post_e60be3cd'.
2025-08-25 20:55:24,115 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-25 20:55:24,115 - Engine - INFO - No notification needed for this action type.
2025-08-25 20:55:24,116 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_65'...
2025-08-25 20:55:24,116 - src.modules.belief_module - INFO - 开始处理用户 'user_65' 的认知流程...
2025-08-25 20:55:24,141 - src.modules.belief_module - INFO - 正在对 2 条短期记忆进行聚类分析...
2025-08-25 20:55:24,141 - src.modules.belief_module - INFO - 有效记忆数量为2，直接作为单个簇处理
2025-08-25 20:55:24,146 - src.modules.belief_module - INFO - 找到 1 个显著议题簇
2025-08-25 20:55:24,146 - src.modules.belief_module - INFO - 正在从记忆簇 'cluster_b218774a' 更新或创建信念...
2025-08-25 20:55:24,150 - src.modules.belief_module - INFO - 用户 'user_65' 没有任何信念，返回第一个记忆命题
2025-08-25 20:55:24,150 - src.modules.belief_module - INFO - 选择的备选信念命题: '最近看一堆婚姻和继承的数据，什么离婚率、财产分割、遗嘱比例，看得人头大。但这些数字根本说不清王姨为啥守着老房子十年，也讲不明白李叔塞存折时那双手有多沉。我邻居老张中风，儿子儿媳轮流照顾，没签协议也没算继承顺序，可每天端汤送药，比啥法律都实在。我信的不是数字，是人和人之间那份温度。再清楚的数据，也比不上你亲眼看见一个人在病床前默默擦汗的样子。说到底，婚姻和继承，是人，不是账本。'
2025-08-25 20:55:24,150 - src.modules.belief_module - INFO - embedding相似度 (0.0000) 低于阈值 (0.9)，创建节点
2025-08-25 20:55:24,150 - src.modules.belief_module - INFO - 使用多线程模式聚合证据
2025-08-25 20:55:24,151 - src.modules.belief_module - INFO - 正在聚合记忆簇 'cluster_b218774a' 的证据（多线程模式）...
2025-08-25 20:55:28,986 - src.modules.belief_module - INFO - 聚合结果（多线程模式） - 支持权重: 0.6473, 反对权重: 0.0000
2025-08-25 20:55:28,986 - src.modules.belief_module - INFO - 正在为用户 'user_65' 创建新信念: '最近看一堆婚姻和继承的数据，什么离婚率、财产分割、遗嘱比例，...'
2025-08-25 20:55:32,045 - src.modules.belief_module - INFO - 使用多线程模式建立信念关系
2025-08-25 20:55:32,045 - src.modules.belief_module - INFO - 正在为信念 'belief_user_65_ce36fd46' 建立关系网络（多线程模式）...
2025-08-25 20:55:32,050 - src.modules.belief_module - INFO - 用户 'user_65' 没有其他信念，无需建立关系
2025-08-25 20:55:32,050 - src.modules.belief_module - INFO - 成功创建信念 'belief_user_65_ce36fd46':
2025-08-25 20:55:32,050 - src.modules.belief_module - INFO -   - 真实度: 0.6473
2025-08-25 20:55:32,050 - src.modules.belief_module - INFO -   - 置信度: 0.3083
2025-08-25 20:55:32,053 - src.modules.belief_module - INFO - 信念 'belief_user_65_ce36fd46' 的置信度 (0.3083) 低于求证阈值 (0.4500)
2025-08-25 20:55:32,053 - src.modules.belief_module - INFO - 正在为用户 'user_65' 触发对信念 'belief_user_65_ce36fd46' 的求证机制...
2025-08-25 20:55:32,054 - src.modules.belief_module - INFO - 从search.json中选择的搜索查询: 【深度辟谣】别再传“同居继承一半财产”了！律师告诉你真实情况是这样！
2025-08-25 20:55:32,054 - src.modules.belief_module - INFO - 搜索结果:
2025-08-25 20:55:32,054 - src.modules.belief_module - INFO - 1. 各位朋友，关于网传的“同居一年继承一半财产”的说法是彻头彻尾的谣言。根据我国《民法典》，同居伴侣不属于法定继承人。继承顺序第一位是配偶、子女、父母。除非逝者生前立下合法有效的遗嘱，明确将财产遗赠给同居伴侣，否则伴侣一分钱也继承不到。房本写谁名就是谁的，这叫不动产登记原则。别再被那些贩卖焦虑的谣言欺骗了！#法律科普 #辟谣 #继承法
2025-08-25 20:55:32,054 - src.modules.belief_module - INFO - 2. 跟爸妈讲清楚了：1. 没领证就不叫“配偶”，没资格按夫妻继承。2. 遗嘱的效力最大，您想给谁就给谁，别人抢不走。3. 房本上是您的名字，那就是您的，谁也拿不走。谣言就是利用老年人信息差和爱子心切的心理，让他们焦虑。大家也快去跟自己爸妈科普一下吧！#家庭辟谣 #中老年关怀 #民法典常识
2025-08-25 20:55:32,054 - src.modules.belief_module - INFO - 生成了求证搜索行为: '【深度辟谣】别再传“同居继承一半财产”了！律师告诉你真实情况是这样！'
2025-08-25 20:55:32,054 - src.modules.belief_module - INFO - 将求证搜索行为加入处理队列...
2025-08-25 20:55:32,054 - Engine - INFO - Added verification action to queue. Queue size: 1
2025-08-25 20:55:32,054 - src.modules.belief_module - INFO - 求证搜索行为已加入队列，将在当前认知处理完成后执行
2025-08-25 20:55:32,054 - src.modules.belief_module - INFO - 已触发信念求证, 查询: '【深度辟谣】别再传“同居继承一半财产”了！律师告诉你真实情况是这样！'
2025-08-25 20:55:32,055 - src.modules.belief_module - INFO - 认知冲击 (0.6473) 超过怀疑阈值 (0.3800)
2025-08-25 20:55:32,055 - src.modules.agent_module - INFO - 正在为用户 'user_65' 演化特质，认知冲击为: 0.6472991753704798, 内容情感强度: 0.5437500000000001
2025-08-25 20:55:32,058 - src.modules.agent_module - INFO - 基于内容情感强度 0.544 调整情绪波动性变化: 0.0045
2025-08-25 20:55:34,902 - src.modules.agent_module - INFO - 用户 'user_65' 的特质已更新:
2025-08-25 20:55:34,902 - src.modules.agent_module - INFO -   - 怀疑主义得分: 0.4447
2025-08-25 20:55:34,902 - src.modules.agent_module - INFO -   - 验证阈值: 0.4824
2025-08-25 20:55:34,902 - src.modules.agent_module - INFO -   - 情绪波动性: 0.6745
2025-08-25 20:55:34,902 - src.modules.belief_module - INFO - 正在传播信念 'belief_user_65_ce36fd46' 的变化，认知冲击为 0.6472991753704798...
2025-08-25 20:55:34,902 - src.modules.belief_module - INFO - 信念 'belief_user_65_ce36fd46' 没有关联边，无需传播
2025-08-25 20:55:34,902 - src.modules.belief_module - INFO - 已触发特质演化和信念传播, 冲击大小: 0.6473, 情感强度: 0.544
2025-08-25 20:55:34,903 - src.modules.belief_module - INFO - 将来自簇 cluster_b218774a 的 2 条记忆移入历史...
2025-08-25 20:55:45,281 - src.modules.belief_module - INFO - 成功移动 2/2 条记忆到历史存档
2025-08-25 20:55:45,282 - Engine - INFO - Cognitive processing for user 'user_65' finished.
2025-08-25 20:55:45,282 - Engine - INFO - Step 5/5: Processing verification queue with 1 actions...
2025-08-25 20:55:45,282 - Engine - INFO - Processing 1 verification actions from queue...
2025-08-25 20:55:45,282 - Engine - INFO - Processing queued verification action for user 'user_65'
2025-08-25 20:55:45,282 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_65' -----
